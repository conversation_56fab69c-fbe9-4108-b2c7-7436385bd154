// ============================================================================
// 基于实际目录结构的DDD重构设计
// ============================================================================

// ============================================================================
// 1. domain/models/contract/contract.ts - 重构后的合同领域模型
// ============================================================================

/**
 * 合同聚合根 - 纯领域模型，移除UI状态
 *
 * 重构要点：
 * 1. 移除所有MobX装饰器和UI状态
 * 2. 保留纯业务属性和方法
 * 3. 使用值对象封装复杂属性
 */
// ============================================================================
// 2. domain/repositories/contract.repository.ts - 合同仓储
// ============================================================================

import { Contract } from '../models/contract/contract';
import { queryContractTemplate } from '../../api/contract/api';
import { queryContractConfig } from '../../utils';

// ============================================================================
// 3. domain/repositories/apply.repository.ts - 申请信息仓储
// ============================================================================

import { queryCardInfo } from '../../api/apply/api';

// ============================================================================
// 4. domain/services/contract-service.ts - 重构后的合同领域服务
// ============================================================================

import { ContractRepository } from '../repositories/contract.repository';
import { ApplyRepository } from '../repositories/apply.repository';
import { getFormPersonalInfo } from '../models/apply/personal-info';
import { getMerchantInstance } from '../models/apply/merchant';
import { getContractInstance } from '../models/contract/contract';

// ============================================================================
// 5. pages/identity/store.ts - 重构后的身份页面Store
// ============================================================================

import { observable, action } from 'mobx';
import { Url } from '@mu/madp-utils';
import { getFormPersonalInfo } from '../../domain/models/apply/personal-info';
import { getMerchantInstance } from '../../domain/models/apply/merchant';
import { getIdentityCardInstance } from '../../domain/models/apply/identity-card';
import { getContractInstance } from '../../domain/models/contract/contract';
import { ApplyService } from '../../domain/services/apply-service';
import { ContractService } from '../../domain/services/contract-service';
import { FormValidationService } from '../../domain/services/form-validation-service';
import { PersonalInfo } from '../../types/apply';

export class Contract {
  private constructor(
    private readonly id: string,
    private readonly type: string,
    private readonly groupId: string,
    private readonly text: string,
    private readonly version: string,
    private readonly forceReadFlag: string,
    private readonly forceReadDuration: number,
    private readonly isOldContract: boolean,
    private readonly infoList: any[] | null,
  ) {}

  /**
   * 工厂方法 - 创建合同实例
   */
  static create(props: {
    id: string;
    type: string;
    groupId: string;
    text: string;
    version?: string;
    forceReadFlag: string;
    forceReadDuration: number;
    isOldContract: boolean;
    infoList?: any[] | null;
  }): Contract {
    return new Contract(
      props.id,
      props.type,
      props.groupId,
      props.text,
      props.version || '1.0',
      props.forceReadFlag,
      props.forceReadDuration,
      props.isOldContract,
      props.infoList || null,
    );
  }

  /**
   * 从现有ContractInfo数据创建
   */
  static fromContractInfo(info: any): Contract {
    return Contract.create({
      id: info.contractGroupId || '',
      type: info.contractCheckItem?.contractType || '',
      groupId: info.contractGroupId || '',
      text: info.contractText || '',
      forceReadFlag: info.forceReadFlag || 'N',
      forceReadDuration: parseInt(info.forceReadDuration) || 0,
      isOldContract: info.oldContractFlag || false,
      infoList: info.contractInfoList,
    });
  }

  // ============================================================================
  // 业务方法
  // ============================================================================

  /**
   * 是否需要强制阅读
   */
  isForceReadRequired(): boolean {
    return this.forceReadFlag === 'Y';
  }

  /**
   * 获取强制阅读时长
   */
  getForceReadDuration(): number {
    return this.forceReadDuration;
  }

  /**
   * 是否为旧版合同
   */
  isLegacyContract(): boolean {
    return this.isOldContract;
  }

  /**
   * 获取合同显示文本
   */
  getDisplayText(): string {
    return this.text;
  }

  // ============================================================================
  // 访问器
  // ============================================================================

  getId(): string { return this.id; }
  getType(): string { return this.type; }
  getGroupId(): string { return this.groupId; }
  getText(): string { return this.text; }
  getVersion(): string { return this.version; }
  getInfoList(): any[] | null { return this.infoList; }
}

/**
 * 合同仓储 - 封装合同数据访问逻辑
 */
export class ContractRepository {
  /**
   * 根据合同组ID获取合同配置
   */
  async findByGroupId(groupId: string): Promise<Contract[]> {
    try {
      // 调用现有的工具函数获取合同配置
      const contractConfig = await queryContractConfig(groupId);

      if (!contractConfig || !contractConfig.contracts) {
        return [];
      }

      // 转换为领域对象
      return contractConfig.contracts.map((item: any) =>
        Contract.create({
          id: item.id || item.contractType,
          type: item.contractType,
          groupId: groupId,
          text: item.text,
          forceReadFlag: item.forceReadFlag || 'N',
          forceReadDuration: parseInt(item.forceReadDuration) || 0,
          isOldContract: !item.contractCode,
        }),
      );
    } catch (error) {
      console.error('获取合同配置失败:', error);
      return [];
    }
  }

  /**
   * 获取强制阅读合同
   */
  async findForceReadContracts(groupId: string): Promise<Contract[]> {
    const contracts = await this.findByGroupId(groupId);
    return contracts.filter(contract => contract.isForceReadRequired());
  }

  /**
   * 根据商户信息确定合同组并获取合同
   */
  async findByMerchantInfo(merchantId: string): Promise<Contract[]> {
    // 业务规则：根据商户ID确定合同组
    let contractGroupId = 'XYDC';

    // 非自营渠道使用不同的合同组
    if (merchantId !== '10000' && merchantId !== '10001') {
      contractGroupId = 'XYDCFZY';
    }

    return await this.findByGroupId(contractGroupId);
  }

  /**
   * 获取合同模板内容
   */
  async getContractTemplate(params: any, title: string): Promise<{title: string; htmlFile: any}> {
    try {
      const htmlFile = await queryContractTemplate(params);
      return { title, htmlFile };
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw new Error('获取合同模板失败');
    }
  }

  /**
   * 批量获取合同模板
   */
  async getContractTemplates(contracts: Contract[], templateParams: any): Promise<any[]> {
    const promises = contracts.map(contract =>
      this.getContractTemplate(templateParams, contract.getText()),
    );

    return await Promise.all(promises);
  }
}

/**
 * 申请信息仓储
 */
export class ApplyRepository {
  /**
   * 获取卡片信息
   */
  async getCardInfo(params: { cardCode: string }): Promise<any> {
    try {
      return await queryCardInfo(params);
    } catch (error) {
      console.error('获取卡片信息失败:', error);
      throw new Error('获取卡片信息失败');
    }
  }
}

/**
 * 合同领域服务 - 重构后专注于业务逻辑编排
 */
export class ContractService {
  private contractRepository: ContractRepository;
  private applyRepository: ApplyRepository;

  constructor() {
    this.contractRepository = new ContractRepository();
    this.applyRepository = new ApplyRepository();
  }

  /**
   * 获取旧合同协议模板参数
   */
  async getOldContractParams(): Promise<{
    contractParams: any[];
    readDuration: string;
    forceReadContractParams: any[];
  }> {
    // 获取领域模型实例
    const personalInfoModel = getFormPersonalInfo();
    const merchantModel = getMerchantInstance();
    const contractModel = getContractInstance();

    // 从仓储获取合同配置
    const contracts = await this.contractRepository.findByMerchantInfo(
      merchantModel.merchantId,
    );

    // 构建模板参数
    const contractParams: any[] = [];
    const forceReadContractParams: any[] = [];
    let readDuration = '0';

    // 基础合同信息
    const baseContractInfo = {
      name: personalInfoModel.custName || '',
      certId: personalInfoModel.certId || '',
      dateNow: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      mobile: '',
      signDate: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      partnerInfoList: [{
        partnerId: merchantModel.merchantId,
        partner: merchantModel.merchantName,
        partnerType: '01',
        partnerContact: merchantModel.custservicePhoneNo,
      }],
    };

    // 处理每个合同
    for (const contract of contracts) {
      const params = {
        contractPreviewData: { baseContractInfo },
        contractCode: contract.getId(),
        contractCategory: contract.getType(),
        scene: 'PREVIEW',
      };

      contractParams.push({
        title: contract.getText(),
        params,
      });

      // 处理强制阅读合同
      if (contract.isForceReadRequired()) {
        readDuration = contract.getForceReadDuration().toString();
        forceReadContractParams.push({
          title: contract.getText(),
          params,
        });
      }
    }

    // 更新现有的合同模型（保持兼容性）
    contractModel.setContractInfo({
      contractParams,
      forceReadContractParams,
      forceReadDuration: readDuration,
    });

    return {
      contractParams,
      readDuration,
      forceReadContractParams,
    };
  }

  /**
   * 获取新合同协议模板参数
   */
  async getNewContractParams(): Promise<{
    contractTemplateList: any[];
    forceContractTemplateList: any[];
    newReadDuration: string;
    isForceReadContract: boolean;
  }> {
    const personalInfoModel = getFormPersonalInfo();
    const merchantModel = getMerchantInstance();
    const contractModel = getContractInstance();

    // 从合同模型获取合同信息列表
    const { contractInfoList } = contractModel;

    if (!contractInfoList || contractInfoList.length === 0) {
      return {
        contractTemplateList: [],
        forceContractTemplateList: [],
        newReadDuration: '0',
        isForceReadContract: false,
      };
    }

    // 转换为领域对象
    const contracts = contractInfoList.map((item: any) =>
      Contract.create({
        id: item.contractCode,
        type: item.contractType,
        groupId: contractModel.contractGroupId,
        text: item.text,
        version: item.contractVersion,
        forceReadFlag: item.forceReadFlag,
        forceReadDuration: parseInt(item.readDuration) || 0,
        isOldContract: false,
      }),
    );

    // 构建模板参数
    const baseContractInfo = {
      name: personalInfoModel.custName || '',
      certId: personalInfoModel.certId || '',
      dateNow: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      mobile: '',
      signDate: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      partnerInfoList: [{
        partnerId: merchantModel.partnerId || merchantModel.merchantId,
        partner: merchantModel.partner || merchantModel.orgName || merchantModel.merchantName,
        partnerType: merchantModel.partnerType || '01',
        partnerContact: merchantModel.custservicePhoneNo,
      }],
    };

    const rechargeInfo = { phoneNo: merchantModel.merchantMobile };

    // 获取合同模板
    const templateParams = {
      contractPreviewData: { baseContractInfo, rechargeInfo },
      interfaceVersion: '3.0',
      scene: 'PREVIEW',
    };

    const contractTemplateList = await this.contractRepository.getContractTemplates(
      contracts,
      templateParams,
    );

    // 筛选强制阅读合同
    const forceReadContracts = contracts.filter(c => c.isForceReadRequired());
    const forceContractTemplateList = contractTemplateList.filter(template =>
      forceReadContracts.some(contract => contract.getText() === template.title),
    );

    const newReadDuration = forceReadContracts.length > 0
      ? forceReadContracts[0].getForceReadDuration().toString()
      : '0';

    // 更新现有的合同模型（保持兼容性）
    contractModel.setContractInfo({
      contractParams: contractTemplateList,
      forceReadContractParams: forceContractTemplateList,
      forceReadDuration: newReadDuration,
    });

    return {
      contractTemplateList,
      forceContractTemplateList,
      newReadDuration,
      isForceReadContract: forceReadContracts.length > 0,
    };
  }

  /**
   * 获取身份信息页合同数据
   */
  async getContractParams(): Promise<void> {
    const contractModel = getContractInstance();
    const { isShowContract, oldContractFlag } = contractModel;

    // 初始化合同协议
    if (isShowContract) {
      if (oldContractFlag) {
        await this.getOldContractParams();
      } else {
        await this.getNewContractParams();
      }
    }
  }
}

/**
 * 身份信息页Store - 重构后分离UI状态和业务逻辑
 *
 * 职责：
 * 1. 管理页面UI状态
 * 2. 协调领域服务调用
 * 3. 为视图层提供数据绑定
 */
class IdentityStore {
  // ============================================================================
  // 领域模型引用（保持现有兼容性）
  // ============================================================================

  /** 表单个人信息实体实例 */
  @observable formPersonalInfo = getFormPersonalInfo();

  /** 商户信息实体实例 */
  @observable merchant = getMerchantInstance();

  /** 身份卡片配置实体实例 */
  @observable identityCard = getIdentityCardInstance();

  /** 合同信息实体实例 */
  @observable contractInfo = getContractInstance();

  // ============================================================================
  // UI状态管理（新增）
  // ============================================================================

  /** 合同弹窗显示状态 */
  @observable showContractsModal = false;

  /** 合同勾选状态 */
  @observable contractChecked = false;

  /** 强制阅读倒计时是否完成 */
  @observable hasCountDown = false;

  /** 是否只显示强制阅读合同 */
  @observable onlyShowForceReadContracts = false;

  /** 页面加载状态 */
  @observable isLoading = false;

  /** 错误信息 */
  @observable error: string | null = null;

  // ============================================================================
  // 领域服务实例
  // ============================================================================

  private contractService: ContractService;

  constructor() {
    this.contractService = new ContractService();
  }

  // ============================================================================
  // 表单操作方法（保持现有接口）
  // ============================================================================

  /**
   * 表单更新方法
   */
  @action.bound
  updateForm = (key: keyof PersonalInfo, value: any) => {
    FormValidationService.updateFormProperty(key, value);
  };

  /**
   * 验证身份证号
   */
  @action.bound
  validateCertId = () => {
    return FormValidationService.checkCertIdFormat();
  };

  /**
   * 表单完整性校验
   */
  @action.bound
  checkForm = (): boolean => {
    return FormValidationService.checkFormData();
  };

  /**
   * 提交表单
   */
  @action.bound
  submitForm = async (): Promise<boolean> => {
    return await FormValidationService.submitForm();
  };

  // ============================================================================
  // 页面初始化方法（保持现有接口）
  // ============================================================================

  /**
   * 页面初始化方法
   */
  async initComp() {
    try {
      this.isLoading = true;
      this.error = null;

      const params = {
        cardCode: Url.getParam('cardCode') || '',
      };
      const cardInfo = await ApplyService.getCardInfo(params);

      return cardInfo;
    } catch (error) {
      this.error = error.message || '初始化失败';
      console.error('页面初始化失败:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 获取合同参数
   */
  @action.bound
  getContractParams = async () => {
    try {
      await this.contractService.getContractParams();
    } catch (error) {
      this.error = error.message || '获取合同参数失败';
      console.error('获取合同参数失败:', error);
    }
  };

  // ============================================================================
  // UI状态管理方法（新增）
  // ============================================================================

  /**
   * 显示合同弹窗
   */
  @action.bound
  showContractModal = (onlyShowForceRead = false) => {
    this.onlyShowForceReadContracts = onlyShowForceRead;
    this.showContractsModal = true;
  };

  /**
   * 隐藏合同弹窗
   */
  @action.bound
  hideContractModal = () => {
    this.showContractsModal = false;
  };

  /**
   * 切换合同勾选状态
   */
  @action.bound
  toggleContractChecked = () => {
    this.contractChecked = !this.contractChecked;
  };

  /**
   * 设置合同勾选状态
   */
  @action.bound
  setContractChecked = (checked: boolean) => {
    this.contractChecked = checked;
  };

  /**
   * 设置倒计时完成状态
   */
  @action.bound
  setCountDownComplete = () => {
    this.hasCountDown = true;
  };

  /**
   * 处理合同点击事件
   */
  @action.bound
  handleContractClick = (isClickCheckbox = false, isClickNextBtn = false) => {
    // 先进行表单校验
    const isFormValid = this.checkForm();
    if (!isFormValid) {
      return;
    }

    const { forceReadContractParams } = this.contractInfo;

    // 点击勾选框触发弹窗、有强读协议且未阅读过协议，则仅展示强读协议
    if (isClickCheckbox && forceReadContractParams.length > 0 && !this.hasCountDown) {
      this.showContractModal(true);
    } else {
      this.showContractModal(false);
    }
  };

  /**
   * 处理复选框点击
   */
  @action.bound
  handleCheckboxClick = () => {
    const isFormValid = this.checkForm();
    if (!isFormValid) {
      return;
    }

    const { forceReadContractParams } = this.contractInfo;

    if (this.contractChecked) {
      this.setContractChecked(false);
      return;
    }

    // 点击勾选时，如果没有强制阅读协议或已读，则直接勾选
    if (forceReadContractParams.length === 0 || this.hasCountDown) {
      this.setContractChecked(true);
      return;
    }

    // 否则显示强制阅读合同
    this.handleContractClick(true);
  };

  /**
   * 提交协议同意
   */
  @action.bound
  submitAgreement = async () => {
    this.setContractChecked(true);
    this.setCountDownComplete();
    this.hideContractModal();
  };
}

// 导出单例实例
const identityStore = new IdentityStore();
export default identityStore;
