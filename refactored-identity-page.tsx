// ============================================================================
// pages/identity/index.tsx - 重构后的身份信息页
// ============================================================================

import Taro, { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUView,
  MUForm,
  MUInput,
  MUText,
  MUImage,
  MUButton
} from '@mu/zui';
import ClickSelector from '@mu/click-selector';
import { AgreementDrawer } from '@mu/agreement';
import PopupPicker from './components/popup-picker';
import IdentityChoice from './components/identity-choice';
import SchoolInfo from './components/school-info';
import ContractChecker from './components/contract-checker';
import dataPool from '../../utils/data-pool';
import IdentityStore from './store';

import './index.scss';

const introImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262516fd52.png';

/**
 * 身份信息页视图层组件 - 重构后
 * 
 * 重构要点：
 * 1. UI状态管理移到Store中
 * 2. 简化组件状态，只保留必要的本地状态
 * 3. 通过Store协调业务逻辑
 * 4. 保持现有的UI交互逻辑不变
 */
@observer
class IdentityPage extends Component {

  async componentDidMount() {
    // 初始化页面数据
    await IdentityStore.initComp();
    await IdentityStore.getContractParams();
  }

  /**
   * 点击协议文本的处理方法
   * 现在委托给Store处理
   */
  onContractClick = (isClickCheckbox = false, isClickNextBtn = false) => {
    IdentityStore.handleContractClick(isClickCheckbox, isClickNextBtn);
  }

  /**
   * 关闭协议预览弹窗
   */
  closeContractsModal = () => {
    IdentityStore.hideContractModal();
  }

  /**
   * 提交表单
   */
  onSubmit = async () => {
    // 1. 协议校验
    if (!IdentityStore.contractChecked) {
      // 弹出协议预览
      this.onContractClick(false, true);
      return;
    }

    // 2. 调用领域服务提交表单（包含表单校验逻辑）
    await IdentityStore.submitForm();
  }

  /**
   * 处理复选框点击
   */
  handleCheckboxClick = () => {
    IdentityStore.handleCheckboxClick();
  }

  /**
   * 提交协议同意
   */
  submitAgreement = async () => {
    await IdentityStore.submitAgreement();
    
    // 如果是点击下一步按钮触发的弹窗，同意后继续提交表单
    // 这里可以根据需要添加额外的逻辑
  }

  render() {
    // 从Store获取所有状态
    const {
      // 领域模型数据
      formPersonalInfo,
      contractInfo,
      
      // UI状态
      showContractsModal,
      contractChecked,
      hasCountDown,
      onlyShowForceReadContracts,
      isLoading,
      error
    } = IdentityStore;

    // 从合同信息中获取模板数据
    const { contractParams, forceReadContractParams } = contractInfo;

    // 如果有错误，显示错误信息
    if (error) {
      return (
        <MUView className="identity3">
          <MUView className="error-message">
            <MUText>{error}</MUText>
          </MUView>
        </MUView>
      );
    }

    // 如果正在加载，显示加载状态
    if (isLoading) {
      return (
        <MUView className="identity3">
          <MUView className="loading">
            <MUText>加载中...</MUText>
          </MUView>
        </MUView>
      );
    }

    return (
      <MUView className="identity3">
        {/* 身份信息表单 */}
        <MUView>
          {/* 基本身份信息 */}
          <MUView className="identity3-title brand-bg-before">
            身份信息
            <MUView className="info-intro">
              <MUImage src={introImg} className="info-intro-img" />
            </MUView>
          </MUView>

          <MUView className="identity3-form">
            <MUForm className="identity3-form_input">
              {/* 姓名输入 */}
              <MUInput
                name="custName"
                title="姓名"
                type="text"
                placeholder="请输入你的真实姓名"
                clear
                enableBlurTrim
                maxLength={50}
                value={formPersonalInfo.custName}
                onChange={(val) => IdentityStore.updateForm('custName', val)}
              />

              {/* 身份证号输入 */}
              <MUInput
                name="certId"
                title="身份证号"
                type="idcard"
                placeholder="同意并输入你的身份证号"
                className="identity3-certId"
                clear
                value={formPersonalInfo.certId}
                enableFormative
                postFixIcon="scan2"
                needCloseKbOnPostFixIconClick
                onChange={(val) => IdentityStore.updateForm('certId', val)}
                onBlur={() => {
                  // 失去焦点时触发完整验证
                  IdentityStore.validateCertId();
                }}
              />
            </MUForm>

            {/* 身份选择器 */}
            <IdentityChoice
              custTypeSelected={formPersonalInfo.custTypeSelected}
              custTypeList={dataPool.custTypeList}
              updateForm={(key: any, val: string) => IdentityStore.updateForm(key, val)}
            />

            {/* 职业选择 */}
            <MUForm className="identity3-form_input">
              <PopupPicker
                name="careerType"
                title="职业"
                placeholder="请选择职业"
                range={dataPool.careerTypeList}
                value={formPersonalInfo.careerType}
                onClickItem={(val: string) => IdentityStore.updateForm('careerType', val)}
              />

              {/* 月收入选择 */}
              <PopupPicker
                name="incomeRange"
                title="月收入"
                placeholder="请选择月收入"
                range={dataPool.incomeRangeList}
                value={formPersonalInfo.incomeRange}
                onClickItem={(val: string) => IdentityStore.updateForm('incomeRange', val)}
              />
            </MUForm>
          </MUView>

          {/* 学校信息 */}
          <SchoolInfo
            formData={{
              highestDegree: formPersonalInfo.highestDegree,
              schoolName: formPersonalInfo.schoolName,
              graduateYear: formPersonalInfo.graduateYear
            }}
            studentDegreeList={dataPool.studentDegreeList}
            updateForm={(key: any, val: string) => IdentityStore.updateForm(key, val)}
          />
        </MUView>

        {/* 协议和提交按钮区域 */}
        <MUView className="contract-btn_block">
          {/* 协议勾选 */}
          <ContractChecker
            contractText={contractInfo.contractText}
            contracts={[contractInfo.contractCheckItem]}
            checkedValue={contractChecked}
            beforeContractText="同意"
            outerControl
            onContractClick={this.onContractClick}
            handleCheckboxClick={this.handleCheckboxClick}
          />

          {/* 协议预览弹窗 */}
          {showContractsModal && (
            <AgreementDrawer
              agreementViewProps={{
                list: onlyShowForceReadContracts ? forceReadContractParams : contractParams,
                current: 0
              }}
              submit={this.submitAgreement}
              show={showContractsModal}
              close={this.closeContractsModal}
              totalCount={!hasCountDown ? contractInfo.forceReadDuration : 0}
            />
          )}

          {/* 提交按钮 */}
          <MUView className="button-block">
            <MUButton
              type="primary"
              className=""
              onClick={this.onSubmit}
            >
              下一步
            </MUButton>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}

export default IdentityPage;
