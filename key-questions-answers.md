# 关键问题解答

## 问题1：纯领域模型移除@observable后，如何与视图层进行数据绑定？

### 解决方案：保留MobX响应式，但只用于业务属性

```typescript
// ✅ 改进方案：保留@observable，但只用于业务属性
export class Contract {
  @observable private id: string;
  @observable private type: string;
  @observable private forceReadFlag: string;
  @observable private forceReadDuration: number;
  // ... 其他业务属性

  // ❌ 移除UI状态
  // @observable isShowContract = false;        // UI状态
  // @observable contractParams: any[] = [];   // UI数据
  // @observable contractCheckItem: any = {};  // UI状态

  // ✅ 使用computed保持响应式
  @computed
  get isForceReadRequired(): boolean {
    return this.forceReadFlag === 'Y';
  }

  @computed
  get readDuration(): number {
    return this.forceReadDuration;
  }
}
```

### 关键原则：
1. **保留业务属性的响应式**：业务数据需要与视图绑定
2. **移除UI状态**：UI状态移到页面Store中管理
3. **使用computed**：计算属性保持响应式，同时体现业务逻辑

---

## 问题2：业务逻辑如何沉淀到model层？仓储层直接调API，哪些service要保留？

### 业务逻辑沉淀到Model层

```typescript
export class Contract {
  // ✅ 业务逻辑沉淀到model
  buildTemplateParams(personalInfo: any, merchantInfo: any): any {
    const baseContractInfo = {
      name: personalInfo.custName || '',
      certId: personalInfo.certId || '',
      dateNow: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      // ... 构建逻辑
    };

    return {
      contractPreviewData: { baseContractInfo },
      contractVersion: this.isOldContract ? '' : this.version,
      contractCode: this.id,
      contractCategory: this.type,
      interfaceVersion: this.isOldContract ? '2.0' : '3.0',
      scene: 'PREVIEW'
    };
  }

  // ✅ 业务规则沉淀到model
  isApplicableForMerchant(merchantId: string): boolean {
    const isSelfOperated = merchantId === '10000' || merchantId === '10001';
    const isStandardGroup = this.groupId === 'XYDC';
    const isNonSelfOperatedGroup = this.groupId === 'XYDCFZY';

    return (isSelfOperated && isStandardGroup) || 
           (!isSelfOperated && isNonSelfOperatedGroup);
  }
}
```

### 仓储层直接调用API

```typescript
export class ContractRepository {
  async findByGroupId(groupId: string): Promise<Contract[]> {
    // ✅ 直接调用API，无需中间层
    const contractConfig = await queryContractConfig(groupId);
    
    // ✅ 数据转换在仓储层
    return contractConfig.contracts.map(item => 
      new Contract({
        id: item.contractId,
        type: item.contractType,
        // ... 转换逻辑
      })
    );
  }

  async getContractTemplate(contract: Contract, personalInfo: any, merchantInfo: any): Promise<any> {
    // ✅ 使用model的业务方法
    const params = contract.buildTemplateParams(personalInfo, merchantInfo);
    
    // ✅ 直接调用API
    const htmlFile = await queryContractTemplate(params);
    
    return {
      title: contract.getText(),
      htmlFile: htmlFile,
      contractId: contract.getId(),
      isForceRead: contract.isForceReadRequired,
      readDuration: contract.readDuration
    };
  }
}
```

### Service层简化分析

#### 🗑️ **可以移除/简化的Service**

1. **contract-service.ts** - 大幅简化
   - 原因：大部分业务逻辑移到Contract model
   - 保留：编排逻辑，协调多个对象

#### ✅ **需要保留的Service**

1. **apply-service.ts** - 保留
   - 原因：处理申请流程编排，协调多个领域对象
   - 职责：跨聚合的业务流程

2. **form-validation-service.ts** - 保留
   - 原因：表单验证逻辑，跨领域的验证规则
   - 职责：数据验证和格式化

3. **user-service.ts** - 保留
   - 原因：用户相关的业务逻辑和状态管理
   - 职责：用户聚合的业务操作

#### 简化后的ContractService

```typescript
export class ContractService {
  private contractRepository: ContractRepository;
  private contractCollection: ContractCollection;

  // ✅ 只保留编排逻辑
  async getContractTemplates(contractGroupId: string, personalInfo: any, merchantInfo: any): Promise<any[]> {
    // 1. 获取合同数据
    if (this.contractCollection.isEmpty) {
      await this.initializeContracts(contractGroupId);
    }

    // 2. 获取适用的合同（使用model的业务方法）
    const applicableContracts = this.contractCollection.getApplicableContracts(merchantInfo.merchantId);

    // 3. 生成模板（委托给仓储）
    return await this.contractRepository.getContractTemplates(applicableContracts, personalInfo, merchantInfo);
  }
}
```

---

## 问题3：user模型是单实例，contract模型是多实例，集合维护应该放在哪里？

### 解决方案：创建ContractCollection管理多实例

```typescript
/**
 * 合同集合管理器 - 管理多个合同实例
 */
export class ContractCollection {
  @observable private contracts: Map<string, Contract> = new Map();

  // ✅ 集合操作
  @action
  addContract(contract: Contract): void {
    this.contracts.set(contract.getId(), contract);
  }

  @action
  addContracts(contracts: Contract[]): void {
    contracts.forEach(contract => {
      this.contracts.set(contract.getId(), contract);
    });
  }

  // ✅ 集合级别的业务逻辑
  @computed
  get forceReadContracts(): Contract[] {
    return this.allContracts.filter(contract => contract.isForceReadRequired);
  }

  @computed
  get allContracts(): Contract[] {
    return Array.from(this.contracts.values());
  }

  getApplicableContracts(merchantId: string): Contract[] {
    return this.allContracts.filter(contract => 
      contract.isApplicableForMerchant(merchantId)
    );
  }

  getContractsByGroup(groupId: string): Contract[] {
    return this.allContracts.filter(contract => 
      contract.getGroupId() === groupId
    );
  }
}
```

### 在页面Store中使用

```typescript
class IdentityStore {
  // ✅ 单实例模型直接引用
  @observable formPersonalInfo = getFormPersonalInfo();
  @observable merchant = getMerchantInstance();
  @observable user = getUserInstance();

  // ✅ 多实例模型使用集合管理
  @observable contractCollection: ContractCollection = new ContractCollection();

  // ✅ 通过computed获取响应式数据
  @computed
  get contractText(): string {
    const contracts = this.contractCollection.allContracts;
    return contracts.map(c => c.getText()).join('、');
  }

  @computed
  get forceReadContracts(): Contract[] {
    return this.contractCollection.forceReadContracts;
  }

  @computed
  get hasForceReadContracts(): boolean {
    return this.forceReadContracts.length > 0;
  }
}
```

### 设计原则总结

#### **单实例模型**（如User）
- 直接在Store中引用：`@observable user = getUserInstance()`
- 全局唯一，状态共享

#### **多实例模型**（如Contract）
- 使用集合管理：`@observable contractCollection = new ContractCollection()`
- 集合提供业务查询方法
- 通过computed保持响应式

#### **集合维护位置**
1. **ContractCollection类**：负责集合的增删改查和业务逻辑
2. **页面Store**：持有集合实例，提供UI相关的计算属性
3. **Service层**：负责集合的初始化和数据获取

---

## 总结

### ✅ **解决方案要点**

1. **数据绑定**：保留MobX响应式，但只用于业务属性，移除UI状态
2. **业务逻辑沉淀**：核心业务逻辑移到Model层，Service层只保留编排逻辑
3. **集合维护**：创建Collection类管理多实例，提供集合级别的业务方法

### 🎯 **架构优势**

1. **职责清晰**：Model负责业务逻辑，Collection负责集合管理，Store负责UI状态
2. **响应式保持**：通过computed和observable保持数据绑定
3. **可维护性**：业务逻辑集中在Model层，易于测试和修改
4. **扩展性**：Collection模式可以应用到其他多实例场景

这种设计既解决了DDD违规问题，又保持了开发效率和响应式特性。
