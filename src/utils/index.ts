import { Validator } from '@mu/madp-utils';
import defaultChannelParams from './contract-params';

/**
 * 查找对象数组中，含有目标对象的项
 * @param {Array} rangeArray 待查对象数组
 * @param {object} targetObj 目标对象
 */
export const findObjFromArr = (rangeArray, targetObj) => {
  let index = 0;
  let target = {};
  if (rangeArray && rangeArray.length) {
    target = rangeArray.filter((item, i) => {
      const keys = Object.keys(targetObj);
      if (keys.every((k) => String(item[k]) === String(targetObj[k]))) {
        index = i;
        return true;
      }
      return false;
    })[0] || {};
  }
  return {
    target,
    index
  };
};

/**
  * 校验名称格式
  * @param {String} name 名称
  */
export const checkName = (name: string) => {
  if (Validator.isName) {
    // 仅支持汉字和中文圆点
    return !Validator.isName(name);
  } else {
    return !Validator.isChineseCharacterOnly(name);
  }
};

/**
 * 获取明文身份证年龄
 * @param {String} certId 身份证号
 */
export const getCertIdAge = (certId: string) => {
  const info = Validator.getIdCardInfo(certId);
  return Validator.getAge(info.year, info.month, info.day);
};

/**
 * 从本地配置文件中获取合同配置
 * */
export const queryContractConfig = (catalogueCode = '13A') => {
  const channelcontractConfigList: any[] = [];
  const channelCatalogueAgreementDtoList = defaultChannelParams[catalogueCode];
  channelCatalogueAgreementDtoList.forEach((item: any) => {
    channelcontractConfigList.push(item);
  });
  return channelcontractConfigList;
};