/**
 * 选择器数据池
 */
const dataPool = {
  applyInsureTypeList: [
    {
      key: '1',
      value: '单位员工'
    },
    {
      key: '2',
      value: '个体经营'
    },
    {
      key: '3',
      value: '自由职业'
    }
  ],
  highestDegreeList: [
    {
      key: '11',
      value: '初中及以下'
    },
    {
      key: '12',
      value: '高中/中专'
    },
    {
      key: '22',
      value: '大专'
    },
    {
      key: '20',
      value: '本科'
    },
    {
      key: '30',
      value: '硕士'
    },
    {
      key: '40',
      value: '博士及以上'
    }
  ],
  studentDegreeList: [
    {
      key: '22',
      value: '大专'
    },
    {
      key: '20',
      value: '本科'
    },
    {
      key: '30',
      value: '硕士'
    },
    {
      key: '40',
      value: '博士及以上'
    }
  ],
  applyContactInfoList: {
    F: [
      {
        key: '13',
        value: '父母'
      },
      {
        key: '11',
        value: '配偶'
      },
      {
        key: '15',
        value: '子女'
      },
      {
        key: '17',
        value: '兄弟'
      },
      {
        key: '18',
        value: '姐妹'
      }
    ],
    S: [
      {
        key: '20',
        value: '朋友'
      },
      {
        key: '22',
        value: '同学'
      },
      {
        key: '21',
        value: '同事'
      }
    ]
  },
  incomeRangeList: [
    {
      key: 'A',
      value: '3000及以下'
    },
    {
      key: 'B',
      value: '3001-5000'
    },
    {
      key: 'C',
      value: '5001-8000'
    },
    {
      key: 'D',
      value: '8001-10000'
    },
    {
      key: 'E',
      value: '10001-20000'
    },
    {
      key: 'F',
      value: '20000以上'
    }
  ],
  newIncomeRangeList: [
    {
      key: 'A',
      value: '3千以下'
    },
    {
      key: 'B',
      value: '3千-5千'
    },
    {
      key: 'C',
      value: '5千-8千'
    },
    {
      key: 'D',
      value: '8千-1万'
    },
    {
      key: 'E',
      value: '1万-2万'
    },
    {
      key: 'F',
      value: '2万以上'
    }
  ],
  workingYearRangeList: [
    {
      key: 'A',
      value: '3年以下'
    },
    {
      key: 'B',
      value: '3年(含)-5年'
    },
    {
      key: 'C',
      value: '5年(含)-10年'
    },
    {
      key: 'D',
      value: '10年(含)-15年'
    },
    {
      key: 'E',
      value: '15年及以上'
    }
  ],
  careerTypeList: [
    {
      key: 'GBM10000',
      value: '国家机关/党群组织/社会团体/企事业单位负责人'
    },
    {
      key: 'GBM20000',
      value: '专业技术人员'
    },
    {
      key: 'GBM30000',
      value: '办事人员和有关人员'
    },
    {
      key: 'GBM40000',
      value: '商业/服务业务人员'
    },
    {
      key: 'GBM50000',
      value: '农/林/牧/渔业生产人员'
    },
    {
      key: 'GBM60000',
      value: '生产/运输设备操作人员及有关人员'
    },
    {
      key: 'GBM70000',
      value: '军人'
    },
    {
      key: 'GBM80000',
      value: '不便分类的其他从业人员'
    }
  ],
  companyCharacterList: [
    {
      key: '1',
      value: '公务员、事业单位'
    },
    {
      key: '2',
      value: '央企、上市公司、500强民企、垄断行业和大型持牌金融机构'
    },
    {
      key: '3',
      value: '国企及控股企业'
    },
    {
      key: '4',
      value: '民企'
    },
  ],
  hasHouseList: [
    {
      key: '0',
      value: '无房'
    },
    {
      key: '4',
      value: '有房'
    },
  ],
  incomeCertList: [
    {
      key: '1',
      value: '公积金月均缴纳基数'
    },
    {
      key: '2',
      value: '个税月均缴纳基数'
    },
    {
      key: '3',
      value: '社保月均缴纳基数'
    },
    {
      key: '4',
      value: '月平均工资流水'
    }
  ],
  custDiscoveryChannel: [
    {
      key: 'ADVERTISEMENT',
      value: '广告'
    },
    {
      key: 'REFERRAL',
      value: '亲友推荐'
    },
    {
      key: 'SEARCH',
      value: '自己搜索'
    },
  ]
};

export default dataPool;