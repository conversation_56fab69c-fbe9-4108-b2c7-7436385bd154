// 申请相关 Mock 数据

/**
 * mucfc.apply.apply.queryCardInfo 接口 Mock 数据
 * 
 * 模拟后端返回的原始数据结构，用于本地开发和测试
 */
export const mockQueryCardInfoResponse = {
  // 申请基础信息
  applyCardBaseInfo: {
    applyPersonalInfo: {
      custName: '张三',
      certId: '******************',
      custTypeSelected: 'STUDENT', // 学生
    }
  },
  
  // 扩展信息
  extraMap: {
    custMobile: '***********',
  },
  
  // 商户信息
  merchantInfo: {
    merchantId: '10000',
    merchantName: '招联消费金融有限公司',
    partnerType: 'SELF',
    partnerId: 'ZL001',
    partner: '招联金融',
    orgId: 'ORG_ZL_001',
    orgName: '招联消费金融',
    custservicePhoneNo: '400-8866-885',
  },
  
  // 卡片属性信息
  cardPropertyInfo: {
    cardExtraProperty: {
      // 身份信息是否可修改
      identityInfoModifiable: 'Y',
      // 是否展示贷款告知书
      BASIC_loanNotice: 'Y',
      // 提交按钮文案
      identityButtonText: '下一步',
      // 是否展示OCR
      showOcr: 'Y',
      // 身份选择器类型
      custTypeValue: 'STUDENT,OFFICE_WORKER,FREELANCER',
      // 是否隐藏合同（N-不隐藏，Y-隐藏）
      hideContract: 'N',
      // 合同组ID
      contractGroupId: 'XYDC',
    }
  },
  
  // 合同信息列表
  contracts: [
    {
      contractType: 'USER_AGREEMENT',
      contractText: 'mock用户服务协议',
      text: 'mock用户服务协议',
      contractCode: 'USER_001',
      htmlFile: '/contracts/user-agreement.html',
      forceReadFlag: 'Y',
      forceReadDuration: '30',
      contractGroupId: 'XYDC',
      oldContractFlag: false,
    },
    {
      contractType: 'PRIVACY_POLICY',
      contractText: '隐私政策',
      text: '隐私政策',
      contractCode: 'PRIVACY_001',
      htmlFile: '/contracts/privacy-policy.html',
      forceReadFlag: 'N',
      forceReadDuration: '0',
      contractGroupId: 'XYDC',
      oldContractFlag: false,
    },
    {
      contractType: 'LOAN_APPLY_NOTICE',
      contractText: '贷款申请告知书',
      text: '贷款申请告知书',
      contractCode: 'LOAN_NOTICE_001',
      htmlFile: '/contracts/loan-notice.html',
      forceReadFlag: 'Y',
      forceReadDuration: '15',
      contractGroupId: 'XYDC',
      oldContractFlag: false,
    },
    {
      contractType: 'CREDIT_AUTHORIZATION',
      contractText: '征信授权书',
      text: '征信授权书',
      contractCode: 'CREDIT_001',
      htmlFile: '/contracts/credit-authorization.html',
      forceReadFlag: 'Y',
      forceReadDuration: '20',
      contractGroupId: 'XYDC',
      oldContractFlag: false,
    }
  ]
};

/**
 * 非自营渠道的 Mock 数据
 */
export const mockQueryCardInfoResponseForThirdParty = {
  ...mockQueryCardInfoResponse,
  merchantInfo: {
    ...mockQueryCardInfoResponse.merchantInfo,
    merchantId: '20001', // 非自营商户
    merchantName: '合作伙伴机构',
    partnerType: 'THIRD_PARTY',
    partnerId: 'PARTNER_001',
    partner: '第三方合作机构',
    orgId: 'ORG_PARTNER_001',
    orgName: '合作伙伴机构',
    custservicePhoneNo: '400-1234-567',
  },
  cardPropertyInfo: {
    cardExtraProperty: {
      ...mockQueryCardInfoResponse.cardPropertyInfo.cardExtraProperty,
      contractGroupId: 'XYDCFZY', // 非自营渠道使用不同的合同组ID
    }
  }
}; 