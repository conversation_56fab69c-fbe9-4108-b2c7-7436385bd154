// 申请相关API封装 
import { apiHost, fetch } from '@mu/business-basic';
import { QueryCardInfoParams, CardInfoResponse } from '@types/api';
import { cardInfoTranslator } from './translator';
import { mockQueryCardInfoResponse } from './mock';
import { MockConfig } from '@constants/mock-config';

/**
 * 查询卡片信息
 *
 * 调用后端接口获取身份信息页所需的所有数据，包括：
 * - 个人信息（姓名、身份证号、职业、收入等）
 * - 商户机构信息（商户号、机构名称、联系方式等）
 * - 身份信息页卡片属性（是否可修改、按钮文案、OCR配置等）
 * - 合同信息（是否展示合同、合同列表等）
 *
 * @param params 查询参数对象
 * @param params.cardCode 卡片编码，用于标识具体的卡片类型
 * @returns 经过转换的卡片信息对象
 * @throws 当网络请求失败时抛出异常
 *
 * @example
 * ```typescript
 * // 获取卡片信息
 * const cardInfo = await queryCardInfo({ cardCode: 'IDENTITY_CARD_01' });
 * console.log(cardInfo.personalInfo.custName); // 用户姓名
 * console.log(cardInfo.identityCard.identityButtonText); // 按钮文案
 * console.log(cardInfo.merchantInfo.merchantName); // 商户名称
 * console.log(cardInfo.contractInfo.isShowContract); // 是否展示合同
 * ```
 */
async function queryCardInfo(params: QueryCardInfoParams): Promise<CardInfoResponse> {
  let response: any;
  
  if (MockConfig.ENABLE_MOCK) {
    // Mock 模式：返回模拟数据
    if (MockConfig.SHOW_MOCK_LOGS) {
      console.log('🎭 Mock Mode: 使用模拟数据代替真实接口调用', { params });
    }
    response = mockQueryCardInfoResponse;
    
    // 模拟网络延迟
    if (MockConfig.MOCK_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, MockConfig.MOCK_DELAY));
    }
  } else {
    // 真实接口调用
    try {
      response = await fetch(
        `${apiHost.mgp}?operationId=mucfc.apply.apply.queryCardInfo`, {
          data: params,
          autoLoading: false,
          autoToast: false,
        },
      );
    } catch (error) {
      // 接口调用失败时自动降级到 mock 数据
      console.warn('⚠️ API 调用失败，自动降级到 Mock 数据:', error);
      response = mockQueryCardInfoResponse;
    }
  }
  
  return cardInfoTranslator(response);
}

/**
 * 提交申请信息
 * @param params 提交参数
 * @returns 提交结果
 */
async function submitApplyInfo(params: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.apply.apply.submitApplyInfo`, {
        data: params,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export {
  queryCardInfo,
  submitApplyInfo,
}; 