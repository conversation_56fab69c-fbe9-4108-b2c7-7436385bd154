// 合同相关API封装
import { apiHost, fetch } from '@mu/business-basic';
import { contractTemplateTranslator } from './translator';
import { MockConfig } from '@constants/mock-config';

/**
 * Mock合同模板响应数据
 */
const mockContractTemplateResponse = {
  contractList: [
    {
      htmlFile: `
        <html>
          <head>
            <title>用户服务协议</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; text-align: center; }
              .content { line-height: 1.6; }
              .section { margin: 20px 0; }
            </style>
          </head>
          <body>
            <h1>用户服务协议</h1>
            <div class="content">
              <div class="section">
                <h2>第一条 协议的范围和接受</h2>
                <p>本协议是您与我们之间关于使用本平台服务的法律协议。通过访问或使用我们的服务，您表示同意遵守本协议的所有条款和条件。</p>
              </div>
              <div class="section">
                <h2>第二条 服务的提供</h2>
                <p>我们将按照本协议的条款向您提供服务。我们保留随时修改、暂停或终止服务的权利，恕不另行通知。</p>
              </div>
              <div class="section">
                <h2>第三条 用户义务</h2>
                <p>您同意按照适用的法律法规使用我们的服务，不得从事任何非法、有害或不当的活动。</p>
              </div>
              <div class="section">
                <h2>第四条 隐私保护</h2>
                <p>我们重视您的隐私，将按照我们的隐私政策处理您的个人信息。</p>
              </div>
              <div class="section">
                <h2>第五条 免责声明</h2>
                <p>在适用法律允许的最大范围内，我们对因使用或无法使用服务而导致的任何损害不承担责任。</p>
              </div>
            </div>
          </body>
        </html>
      `,
      contractName: '用户服务协议',
      contractCode: 'USER_AGREEMENT_001'
    }
  ]
};

/**
 * 查询合同模板信息
 * @param params 查询参数
 * @returns 合同模板信息
 */
async function queryContractTemplate(params: any): Promise<any> {
  // 如果开启Mock模式，直接返回Mock数据
  if (MockConfig.ENABLE_MOCK) {
    // 模拟网络延迟
    if (MockConfig.MOCK_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, MockConfig.MOCK_DELAY));
    }
    
    return contractTemplateTranslator(mockContractTemplateResponse);
  }
  
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.contract.getContractInfo`, {
        data: params,
        autoLoading: false,
        autoToast: false,
      },
    );
    
    return contractTemplateTranslator(response);
  } catch (error) {
    // 接口调用失败时降级到Mock
    return contractTemplateTranslator(mockContractTemplateResponse);
  }
}

export {
  queryContractTemplate,
}; 