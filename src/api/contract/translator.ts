// 合同相关数据转换器

/**
 * 合同模板转换器
 * 从接口响应中提取第一个合同的htmlFile字段
 * @param response 接口响应数据
 * @returns 合同模板的htmlFile内容
 */
function contractTemplateTranslator(response: any): string {
  try {
    // 从响应中获取contractList
    const { contractList = [] } = response || {};
    
    // 获取第一个合同元素
    const firstContract = contractList[0];
    const { htmlFile } = firstContract || {};
    
    // 返回htmlFile字段，如果不存在则返回空字符串
    return htmlFile || '';
  } catch (error) {
    return '';
  }
}

export {
  contractTemplateTranslator,
}; 