import { useState, useEffect } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUForm,
  MURadio,
} from '@mu/zui';
import ClickSelector from '@mu/click-selector';
import './index.scss';

/**
 * 表单数据接口
 */
interface FormData {
  custTypeSelected?: string;
  [key: string]: any;
}

/**
 * 范围数据项接口
 */
interface RangeItem {
  key: string;
  value: string;
}

/**
 * IdentityChoice 组件属性接口
 */
interface IdentityChoiceProps {
  /** 表单数据 */
  formData: FormData;
  /** 范围数据 */
  range?: { [key: string]: any };
  /** 更新表单回调 */
  updateForm: (value: string) => void;
  /** 埋点内容 */
  beaconContent?: { [key: string]: any } | null;
  /** 是否显示星号 */
  ifShowStar?: boolean;
  /** 是否单行显示 */
  ifSingleRow?: boolean;
  /** 标题类型 */
  titleType?: string;
  /** 是否显示标题 */
  showTitle?: boolean;
  /** 选项数量 */
  count?: number;
  /** 是否需要固定宽度 */
  needFixWidth?: boolean;
}

const IdentityChoice: React.FC<IdentityChoiceProps> = ({
  formData,
  updateForm,
  ifShowStar = false,
  ifSingleRow = false,
  showTitle = true,
  count,
  needFixWidth = true,
}) => {
  const [formDataNew, changeFormData] = useState(formData);

  useEffect(() => {
    changeFormData(formData);
  }, [formData]);

  const onClickSelector = (val: string) => {
    updateForm(val);
  };

  const rangMap: RangeItem[] = [
    {
      key: 'C01001',
      value: '我已工作'
    },
    {
      key: 'C01002',
      value: '我是学生'
    }
  ];

  return (
    <MUForm titleType="list" title={showTitle ? '身份选择' : ''} ifShowStar={ifShowStar}>
      {ifSingleRow
        ? <MURadio
            ifShowStar={ifShowStar}
            count={2}
            title="身份选择"
            type="badge"
            className="identity-choice-radio"
            options={rangMap && rangMap.map(
              (o) => ({ value: o.key, label: o.value })
            )}
            value={formDataNew.custTypeSelected}
            onClick={(val) => { onClickSelector(val); }}
        />
        : <ClickSelector
            count={count || 2}
            needFixWidth={needFixWidth}
            type={'IdentityChoice'}
            range={rangMap || []}
            value={formDataNew.custTypeSelected}
            onClickItem={(val) => { onClickSelector(val); }}
        />}
    </MUForm>
  );
};

export default observer(IdentityChoice);
