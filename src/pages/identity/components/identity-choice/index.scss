@import "~@mu/zui/dist/style/mixins/index.scss";

/* 兼容小程序，参考zui直接用了标签名，但开发工具会有warning */
@include weappBottomLine("mu-radio", ".mu-radio", 30px);

.apply-radio.mu-radio--badge {
  .mu-radio__option-group {
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.identity-choice-radio {
  .mu-radio__title {
    width: 250px;
  }
  .mu-radio__option-group {
    .mu-radio__option {
      margin-right: 15px;
    }
  }
}
