import Taro, { useState, useEffect } from '@tarojs/taro';
import {
  MUView, MUListItem, MUText,
  MUCategorySelector
} from '@mu/zui';
import { debounce } from '@mu/madp-utils';
import { findObjFromArr } from '@utils';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('./index.scss');
}

interface RangeItem {
  key: string;
  value: string;
  [key: string]: any;
}

interface PopupPickerProps {
  title: string;
  selectorTitle: string;
  placeholder: string;
  range: RangeItem[];
  onClickItem: (key: string) => void;
  value?: string;
  beaconId: string;
  beaconContent?: any;
  arrow?: string;
  height?: number;
  ifShowStar?: boolean;
  labelFieldName?: string;
  valueFieldName?: string;
  ifSingleRow?: boolean;
}

const defaultProps: Partial<PopupPickerProps> = {
  value: '',
  beaconContent: {},
  arrow: 'mu-icon-arrow-right',
  height: 950
};

const PopupPicker: React.FC<PopupPickerProps> = (props) => {
  const {
    title, placeholder, range, onClickItem, beaconId, value, beaconContent, arrow, ifShowStar, selectorTitle, labelFieldName, valueFieldName,
    ifSingleRow, height
  } = props;

  /*
      title:表单标题, placeholder, range：选择器列表数组, onClickItem:选择器元素点击事件, beaconId, value, beaconContent, arrow, ifShowStar:是否红星必填, selectorTitle：选择器标题, labelFieldName:选择器label对应传入数组的key值(字符串), valueFieldName：选择器value对应传入数组的key值（编码）,
      ifSingleRow:(是否单列排列，false则两列排列)
   */
  const [rangeNew, updateRange] = useState<RangeItem[]>(range);
  const [isOpened, changeIsOpened] = useState<boolean>(false);
  // 小程序下会预先编译引用的组件，放外面还未拿到props，所以延后执行
  useEffect(() => {
    updateRange(range);
  }, [range]);


  const currentObj = rangeNew && findObjFromArr(rangeNew, { key: value });// 查找value对应的key的目标元素
  // 兼容小程序预编译无props，设置默认值

  let currentItem: Partial<RangeItem> = {};
  if (currentObj && currentObj.target) {
    currentItem = currentObj.target;
  }


  let beaconContentCus = {};
  if (beaconContent && beaconContent.cus) {
    beaconContentCus = beaconContent.cus;
  }
  const swanClick = debounce(() => {
    setTimeout(() => {
      changeIsOpened(!isOpened);
    }, 500);
  }, 500, {
    leading: true, // 指定调用在节流开始前
    trailing: false
  });
  return (
    <MUView className="popup-picker">
      <MUListItem
        ifShowStar={ifShowStar}
        arrow={arrow}
        title={title}
        content={currentItem.value}
        renderContent={<MUText className="placeholder">{currentItem.value ? '' : placeholder}</MUText>}
        beaconId={`${beaconId}Item`}
        onClick={() => { 
          if (process.env.TARO_ENV === 'swan') {
            swanClick();
          } else {
            changeIsOpened(!isOpened);
          }
         }}
        beaconContent={{
          cus: {
            selectedItem: value,
            ...beaconContentCus
          }
        }}
      />
      <MUCategorySelector
        beaconId={`${beaconId}Popup`}
        beaconContent={beaconContent}
        title={selectorTitle}
        isOpened={isOpened}
        dataList={range}
        labelFieldName={labelFieldName}
        valueFieldName={valueFieldName}
        ifSingleRow={ifSingleRow}
        value={value}
        height={Taro.pxTransform(height)}
        onChange={(key) => {
          onClickItem(key);
          changeIsOpened(!isOpened);
        }}
        onClose={() => changeIsOpened(false)}
      />
    </MUView >
  );
};

PopupPicker.options = {
  addGlobalClass: true
};
PopupPicker.config = {
  styleIsolation: 'shared'
};

PopupPicker.defaultProps = defaultProps;

export default PopupPicker;
