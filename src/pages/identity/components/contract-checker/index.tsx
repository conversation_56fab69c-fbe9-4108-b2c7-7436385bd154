import { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON>iew,
  MUContractChecker,
} from '@mu/zui';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('./index.scss');
}

/**
 * 合同条目接口
 */
interface ContractItem {
  id: number;
  text: string;
}

/**
 * 强制阅读合同接口
 */
interface ForcedReadContract {
  name: string;
}

/**
 * ContractChecker 组件属性接口
 */
interface ContractCheckerProps {
  /** 合同列表 */
  contracts: ContractItem[];
  /** 合同点击回调 */
  onContractClick: (event?: any) => void;
  /** 合同状态变更回调 */
  onChangeContractStatus?: (checked: boolean, contractType?: string) => void;
  /** 埋点ID */
  beaconId: string;
  /** 合同文本 */
  contractText: string;
  /** 合同前置文本 */
  beforeContractText?: string;
  /** 复选框点击回调 */
  handleCheckboxClick?: (checked: boolean) => void;
  /** 复选框选中状态 */
  checkedValue?: boolean;
  /** 是否外部控制 */
  outerControl?: boolean;
  /** 是否已授权征信 */
  isRhAuthorized?: boolean;
  /** 合同类型 */
  contractType?: string;
  /** 强制阅读合同是否已勾选 */
  forceReadContractChecked?: boolean;
  /** 强制阅读合同配置 */
  forcedReadContract?: ForcedReadContract;
  /** 合同文本是否换行 */
  contractNowrap?: boolean;
}

/**
 * ContractChecker 组件状态接口
 */
interface ContractCheckerState {
  checked: boolean;
}

class ContractChecker extends Component<ContractCheckerProps, ContractCheckerState> {
  static defaultProps: Partial<ContractCheckerProps> = {
    beforeContractText: '同意',
    checkedValue: false,
    outerControl: false,
    contractType: '',
    isRhAuthorized: false,
    forcedReadContract: {
      name: ''
    },
    contractNowrap: false,
    forceReadContractChecked: false,
    handleCheckboxClick: () => {},
    onChangeContractStatus: () => {},
  };

  timer: any = null;

  constructor(props: ContractCheckerProps) {
    super(props);

    // 倒计时timer
    this.timer = null;

    this.state = {
      checked: props.checkedValue || false,
    };
  }

  componentWillReceiveProps(nextProps: ContractCheckerProps) {
    const { checkedValue } = nextProps;
    if (checkedValue !== this.props.checkedValue && checkedValue !== this.state.checked) {
      this.setState({ checked: checkedValue || false });
    }
  }

  static options = {
    addGlobalClass: true
  }

  /**
   * 点击合同checkbox
   */
  onCheckboxClick = (val: boolean) => {
    const { checked } = this.state;
    const {
      onChangeContractStatus,
      handleCheckboxClick,
      forceReadContractChecked,
      contractType,
      isRhAuthorized
    } = this.props;
    // 点击勾选，判断是否需要拉起强制协议阅读弹窗
    handleCheckboxClick(val);
    // 已勾选
    if (checked) {
      onChangeContractStatus(false, contractType);
      this.setState({
        checked: false
      });
      return;
    }
    // 如果强制协议弹窗已同意，则把值设为true
    if (forceReadContractChecked) {
      onChangeContractStatus(true, contractType);
      if (!isRhAuthorized) { // 如果是信用智能评估页 勾选了协议后又取消了人行征信授权，则不需要改变状态
        return;
      }
      this.setState({
        checked: true
      });
    }
  }

  render() {
    const {
      beaconId,
      onContractClick,
      contracts,
      contractText,
      beforeContractText,
      outerControl,
      contractNowrap
    } = this.props;

    const {
      checked,
    } = this.state;

    return (
      <MUView className="apply-contract-checker">
        <MUContractChecker
          contractNowrap={contractNowrap}
          outerControl={outerControl}
          beaconId={`${beaconId}Checker`}
          beforeContractText={beforeContractText}
          contractText={contractText}
          contracts={contracts}
          checkboxValue={checked}
          onCheckboxClick={this.onCheckboxClick}
          onContractClick={onContractClick}
          className="contract-sheet"
        />
      </MUView>
    );
  }
}

export default ContractChecker;
