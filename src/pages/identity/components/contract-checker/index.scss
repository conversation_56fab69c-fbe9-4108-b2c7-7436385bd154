@import "~@mu/zui/dist/style/variables/default.scss";

.apply-contract-checker {
  .modal {
    &.display-block {
      display: block;
    }

    &.display-none {
      display: none;
    }

    .mu-modal__container {
      width: 90%;
      position: relative;
      overflow: visible;
    }

    &__overflow {
      overflow: hidden;
    }

    &__iframe {
      -webkit-overflow-scrolling: touch;
      overflow-y: scroll;
      height: 600px;
    }

    &__btn {
      padding: $spacing-v-xl $spacing-h-lg;

      &__text {
        display: flex;
        align-items: center;

        .mu-icon {
          margin-right: $spacing-h-sm;
        }
      }
    }

    &__icon {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translate(-50%, 200%);
    }
  }

  .mu-contract-checker {
    padding-right: 30px;
    padding-bottom: 0;
    margin: 50px 0;
  
    .mu-radio__option {
      margin: 0;
    }
  }

  .contract-content {
    background: #fff;
  }

  .contract-sheet {
    .at-action-sheet {
      &__item {
        white-space: normal;
      }
    }
  }
}
