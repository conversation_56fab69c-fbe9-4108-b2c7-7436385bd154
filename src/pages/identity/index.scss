/* 身份信息页样式 */
/* stylelint-disable selector-type-no-unknown */
@import "~@mu/zui/dist/style/variables/default.scss";
@import "~@mu/zui/dist/style/mixins/index.scss";
@import '~@mu/click-selector/dist/style/index.scss';
@import '~@mu/agreement/dist/style/components/drawer.scss';

.identity3 {
  background-color: #f3f3f3;
  flex: 1;
  display: flex;
  flex-direction: column;

  .button-block {
    margin: 50px $spacing-h-xl;

    .opacity {
      opacity: 0.3;
    }
  }

  .apply-image-with-status {
    img {
      display: block;
    }
  }

  &-certId {
    padding-right: 18px;
  }

  &-custname-onlyRead {
    .at-input__input {
      opacity: 0.5;
      -webkit-text-fill-color: #333;
    }
  }

  &-certId-onlyRead {
    padding-right: 18px;

    .bankcard_idcard_input {
      opacity: 0.5;
    }

    .at-input__customize-keyboard-input .taro-text {
      opacity: 0.5;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    background: #fff;
    padding: $spacing-v-lg 0;

    /* 改成跟.mu-form__title--list一样，使得进度条垂直居中 */
    margin-top: 20px;
    font-size: $font-size-h2;
    font-weight: 500;
    color: #333;

    @include hairline-bottom-relative();

    &::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: $font-size-h2;
      margin-right: 22px;
    }

    .info-intro {
      display: flex;
      width: 50px;
      height: 100%;
      justify-content: center;
      align-items: center;

      &-img {
        width: 32px;
        height: 32px;
        display: block;
      }
    }
  }

  &-form {
    display: flex !important;
    align-items: stretch;
    background-color: $color-bg;

    mu-form {
      flex: 1;
      flex-shrink: 0;
    }

    mu-view:last-of-type {
      flex: 0;
    }

    mu-text {
      padding: 0 5px;
    }

    &_input {
      flex: 1;
      flex-shrink: 0;

      .at-input__title {
        min-width: 140px;
        width: 140px;
      }

      .at-input__customize-keyboard-input {
        padding-right: 0;
      }
    }

    &_scan {
      width: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      color: #333;
      font-size: $font-size-sm;
      flex-shrink: 0;
      padding: 40px 24px;

      @include hairline-left();

      &--img {
        width: 42px;
        height: 38px;
        margin-bottom: 2px;
      }

      &--text {
        display: block;
        padding: 0 5px;
      }
    }
  }
  .fixFormStyle {
    display: block !important;
  }

  &__top {
    display: flex;

    @include hairline-bottom-relative();

    &__img {
      width: 100%;
      height: auto;
    }
  }

  &__cust-selector {
    background: #fff;
    display: flex;
    padding: $spacing-v-sm 0;

    @include hairline-top();

    // 小程序编译额外多一层标签
    mu-view {
      flex: 1;
    }

    &__item {
      &:first-child {
        @include hairline-right-relative();
      }

      flex: 1;
      flex-direction: column;
      display: flex;
      justify-content: center;
      align-items: center;

      .text {
        color: $color-text-base;
        font-size: $font-size-base;
      }

      .icon {
        width: $icon-size-xxl;
        height: $icon-size-xxl;
        background: center center / 100% 100% no-repeat;

        &--work {
          background-image: url("https://file.mucfc.com/abf/1/38/202301/2023011710322486dc23.png");

          &--actived {
            background-image: url("https://file.mucfc.com/abf/1/38/202301/20230117103224060e49.png");
          }
        }

        &--student {
          background-image: url("https://file.mucfc.com/abf/1/38/202301/20230117103224d30c55.png");

          &--actived {
            background-image: url("https://file.mucfc.com/abf/1/38/202301/202301171032242c06e8.png");
          }
        }

        &--doctor {
          background-image: url("https://file.mucfc.com/abf/1/0/202212/20221201112617a22584.png");

          &--actived {
            background-image: url("https://file.mucfc.com/abf/1/0/202212/20221201112617ed320d.png");
          }
        }

        &--nurse {
          background-image: url("https://file.mucfc.com/abf/1/0/202212/202212011126332110b2.png");

          &--actived {
            background-image: url("https://file.mucfc.com/abf/1/0/202212/20221201112633f8f756.png");
          }
        }
      }
    }
  }

  &-contract {
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    .mu-form-wrap {
      height: 0;
      display: flex;
      flex-direction: column;
    }

    &_form {
      flex: 1;
      height: 0;
      overflow: scroll;
      -webkit-overflow-scrolling: touch;
    }
  }

  .idCard_tip_dialog {
    .mu-dialog__container {
      width: 100%;
      background: #FFFFFF;
      border-radius: 16px 16px 0 0;
      position: fixed;
      bottom: 0;
      top: auto;
      left: 0;
      transform: translate(0);
    }

    .mu-dialog__content {
      padding: 40px 0 50px 0;
      max-height: none;
    }

    &-title {
      font-weight: bold;
      font-size: 36px;
      color: #333333;
      text-align: center;
      line-height: 36px;
    }

    &-close {
      width: 45px;
      height: 45px;
      position: fixed;
      top: 0.64rem;
      right: 0.85333rem;
    }

    &-imgs {
      width: 669px;
      height: 572px;
      margin: 46px 31px 30px 50px;

    }

    &-button {
      margin: 0 30px;
    }
  }
}

.mu-modal__content .content-text b {
  color: #808080 !important;
}

.user-ensure {
  .agreement-content {
    padding: 0 !important;
  }

  .agreement-content>p {
    text-indent: 0 !important
  }
}

.not_id_card {
  width: 100%;
  text-align: center;
  margin-top: -32px;

  .not_id_card_tip {
    font-size: 26px;
    font-family: PingFangSC;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0;
    display: inline-block;

    &-left {
      display: inline-block;
      color: #808080ff;
    }

    &-right {
      display: inline-block;
      margin-left: 15px;
      position: relative;

      &-text {
        color: #3477FF;
      }
    }
  }
}