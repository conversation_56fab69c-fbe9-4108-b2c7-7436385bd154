// 身份信息页store（控制层） 
import { observable, action } from 'mobx';
import { Url } from '@mu/madp-utils';
import { getFormPersonalInfo } from '@models/apply/personal-info';
import { getMerchantInstance } from '@models/apply/merchant';
import { getIdentityCardInstance } from '@models/apply/identity-card';
import { getContractInstance } from '@models/contract/contract';
import { ApplyService } from '@services/apply-service';
import { ContractService } from '@services/contract-service';
import { FormValidationService } from '@services/form-validation-service';
import { PersonalInfo } from '@types/apply';

/**
 * 身份信息页控制层 Store - DDD 控制层
 *
 * 控制层负责管理 UI 状态、处理用户交互、对领域对象和领域服务进行编排和组合。
 * 一般不包含具体的业务逻辑，业务逻辑应该放在领域层中。
 *
 * 职责范围：
 * 1. 管理页面级别的 UI 状态
 * 2. 协调领域服务的调用
 * 3. 为视图层提供数据和操作接口
 * 4. 处理页面生命周期相关的逻辑
 *
 * 设计原则：
 * - 薄控制层：不包含复杂的业务逻辑
 * - 编排协调：负责协调多个领域服务的调用
 * - 状态管理：管理页面相关的 UI 状态
 * - 响应式：通过 MobX 实现响应式数据绑定
 *
 * @example
 * ```typescript
 * // 在视图层中使用
 * const { formPersonalInfo, merchant, identityCard } = IdentityStore;
 *
 * // 初始化页面数据
 * await IdentityStore.initComp();
 * ```
 */
class IdentityStore {
  /** 表单个人信息实体实例 - 用户填写的数据 */
  @observable formPersonalInfo = getFormPersonalInfo();

  /** 商户信息实体实例 - 通过领域层获取 */
  @observable merchant = getMerchantInstance();

  /** 身份卡片配置实体实例 - 通过领域层获取 */
  @observable identityCard = getIdentityCardInstance();

  /** 合同信息实体实例 - 通过领域层获取 */
  @observable contractInfo = getContractInstance();

  /**
   * 表单更新方法
   * 
   * 协调领域服务更新个人信息表单数据
   * @param key 属性名
   * @param value 属性值
   */
  @action.bound
  updateForm = (key: keyof PersonalInfo, value: any) => {
    FormValidationService.updateFormProperty(key, value);
  }

  /**
   * 验证身份证号
   * 
   * 调用领域服务对当前身份证号进行完整验证
   */
  @action.bound
  validateCertId = () => {
    return FormValidationService.checkCertIdFormat();
  }

  /**
   * 表单完整性校验
   * 
   * 调用领域服务对整个表单进行校验
   * @returns 校验是否通过
   */
  @action.bound
  checkForm = (): boolean => {
    return FormValidationService.checkFormData();
  }

  /**
   * 提交表单
   * 
   * 调用领域服务提交表单（包含校验逻辑）
   * @returns 提交是否成功
   */
  @action.bound
  submitForm = async (): Promise<boolean> => {
    return await FormValidationService.submitForm();
  }

  /**
   * 页面初始化方法
   *
   * 负责协调身份信息页相关数据的获取，包括：
   * 1. 调用申请领域服务获取卡片信息
   * 2. 自动更新所有相关的领域模型实例
   *
   * 这是一个编排方法，协调申请领域服务的调用，
   * 不包含具体的业务逻辑。
   *
   * @param params 查询参数对象
   * @param params.cardCode 卡片编码，用于标识具体的卡片类型
   * @returns 卡片信息数据
   * @throws 当服务调用失败时抛出异常
   *
   * @example
   * ```typescript
   * // 初始化身份信息页
   * try {
   *   await IdentityStore.initComp();
   *   console.log('表单数据:', IdentityStore.formPersonalInfo);
   *   console.log('商户信息:', IdentityStore.merchant);
   *   console.log('卡片配置:', IdentityStore.identityCard);
   * } catch (error) {
   *   console.error('初始化失败:', error);
   * }
   * ```
   */
  async initComp() {
    // 调用申请领域服务获取卡片信息
    // 服务层会自动更新所有相关的领域模型实例
    const params = {
      cardCode: Url.getParam('cardCode') || '',
    }
    const cardInfo = await ApplyService.getCardInfo(params);

    return cardInfo;
  }

  /**
   * 获取合同参数
   */
  getContractParams = async () => {
    ContractService.getContractParams();
  }
}

// 导出单例实例
const identityStore = new IdentityStore();
export default identityStore; 