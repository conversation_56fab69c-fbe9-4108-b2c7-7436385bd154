# IdentityTemplate

身份信息页前端模板

基于DDD架构的身份信息页面模板。

## 🚀 快速开始

1. 在 `app.scss` 中引入 `click-selector` 点击选择器与合同弹窗样式文件。

```scss
@import '~@mu/click-selector/dist/style/index.scss';
@import '~@mu/agreement/dist/style/components/drawer.scss';
```

2. 身份信息页模板所需依赖如下，检查本地是否存在遗漏的依赖。

```json
"dependencies": {
  "@mu/lui": "2.1.0-alpha.64",
  "@mu/basic-library": "1.7.16",
  "@mu/zui": "1.24.3-beta.37",
  "@mu/click-selector": "1.2.1-beta.15",
  "@mu/vague-search": "1.0.1-beta.11",
  "@mu/agreement": "1.5.23-nostyle.1"
}
```

3. 安装依赖，运行项目。

```bash
npm install
npm run dev:h5
```

## 🏗️ 项目结构

```
src/code/
├── api/                            # 接口层 - 对接后端API
│   ├── apply/                      # 申请相关接口
│   │   ├── api.ts                  # 申请API实现
│   │   ├── translator.ts           # 数据转换器
│   │   └── mock.ts                 # Mock数据
│   └── contract/                   # 合同相关接口
├── constants/                      # 常量配置
├── domain/                         # 领域层 - 业务逻辑核心
│   ├── models/                     # 领域模型
│   │   ├── apply/                  # 申请相关模型
│   │   │   ├── apply.ts            # 申请主模型
│   │   │   ├── personal-info.ts    # 个人信息子对象
│   │   │   ├── identity-card.ts    # 身份信息页子对象
│   │   │   └── merchant.ts         # 商户信息子对象
│   │   └── contract/               # 合同相关模型
│   │       └── contract.ts         # 合同模型
│   └── services/                   # 领域服务
│       ├── apply-service.ts        # 申请领域服务
│       ├── contract-service.ts     # 合同领域服务
│       └── form-validation-service.ts # 表单校验服务
├── pages/                          # 视图层
│   ├── components/                 # 页面级组件
│   │   ├── contract-checker/       # 协议勾选组件
│   │   ├── identity-choice/        # 身份选择组件
│   │   ├── popup-picker/           # 弹窗选择器
│   │   └── school-info/            # 学校信息组件
│   ├── index.tsx                   # 主页面组件
│   ├── index.scss                  # 页面样式
│   ├── store.ts                    # 控制层
│   └── README.md                   # 模板说明文档
├── types/                          # TS类型定义
│   ├── api.d.ts                    # API相关类型
│   ├── apply.d.ts                  # 申请相关类型
│   └── contract.d.ts               # 合同相关类型
└── utils/                          # 工具函数
```

各个分层说明：

1. 数据接口层 `api`：定义后端接口以及数据转换器。负责发起接口请求，并将后端返回的数据进行二次加工，转换为前端需要的字段。

2. 领域层 `domain`：项目核心层。
- 领域模型 `models` 定义了不同业务的数据对象，包括属性和业务逻辑，例如申请领域模型、合同领域模型。
- 领域服务层 `services` 封装不适合放在领域模型层的领域逻辑。当一个业务逻辑涉及到多个领域模型、需要请求外部接口、或者放到领域模型中会使模型变得臃肿不协调时，应该放在领域服务层中。

3. 视图层（包括 `pages` 和 `components` 中页面和样式）：前端页面交互逻辑、页面样式。视图层中不应该编写业务逻辑，只需要关注页面的展示和交互，因此视图层应该很薄。

4. 控制层（包括 `pages` 和 `components` 下的 store）：管理 UI 状态、处理用户交互、调用领域模型和领域服务提供的函数，一般不包含具体的业务逻辑。控制层被视图层调用。

注意事项：

- 业务逻辑尽可能放在领域层之中，视图层和控制层不要书写复杂的业务操作。

- 业务逻辑首先尝试放在领域模型里，若发现「有异常不自然」，例如需要和其它模型进行交互、需要请求后端接口，则将其放在领域服务层中。

- 视图层不能直接调用领域层的内容，只能调用控制层间接调用领域层提供的内容。

## 📦 技术栈

- **框架**: Taro + React + TypeScript
- **状态管理**: MobX
- **架构模式**: DDD（领域驱动设计）

## 📱 预览图

身份信息页页面截图如下：

![screenshot.png](https://unpkg.mucfc.com/@mu/identity-template@0.1.1-5/screenshot.png)

