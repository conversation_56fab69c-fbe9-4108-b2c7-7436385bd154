// 身份信息页入口（视图层） 
import Taro, { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUView,
  MUForm,
  MUInput,
  MUText,
  MUImage,
  MUButton
} from '@mu/zui';
import ClickSelector from '@mu/click-selector';
import { AgreementDrawer } from '@mu/agreement';
import PopupPicker from './components/popup-picker';
import IdentityChoice from './components/identity-choice';
import SchoolInfo from './components/school-info';
import ContractChecker from './components/contract-checker';
import dataPool from '@utils/data-pool';
import IdentityStore from './store';

import './index.scss';

const introImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262516fd52.png';
// const scanIdImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111263322469c.png';


/**
 * 组件状态接口定义
 */
interface IdentityPageState {
  showContractsModal: boolean;
  contractChecked: boolean;
  hasCountDown: boolean;
  showContractBtn: boolean;
  clickNextBtn: boolean;
  onlyShowForceReadContracts: boolean;
}

/**
 * 身份信息页视图层组件
 * 
 * 负责展示身份信息收集表单，包括：
 * - 基本身份信息（姓名、身份证号）
 * - 身份选择器
 * - 职业信息
 * - 月收入选择
 * - 学校信息
 * - 协议勾选
 * - 提交按钮
 */
@observer
class IdentityPage extends Component<{}, IdentityPageState> {

  constructor(props) {
    super(props);
    this.state = {
      showContractsModal: false,  // 控制协议预览弹窗显示
      contractChecked: false,
      hasCountDown: false, // 合同是否倒计时完成过
      showContractBtn: false, // 是否显示协议按钮
      clickNextBtn: false, // 是否点击了下一步按钮
      onlyShowForceReadContracts: false, // 是否仅展示强读协议
    };
  }

  async componentDidMount() {
    // 初始化页面数据
    await IdentityStore.initComp();
    await IdentityStore.getContractParams();
  }

  /**
   * 点击协议文本的处理方法
   */
  onContractClick = (isClickCheckbox = false, isClickNextBtn = false) => {
    // 先进行表单校验
    const isFormValid = IdentityStore.checkForm();
    if (!isFormValid) {
      return;
    }

    const { contractChecked, hasCountDown } = this.state;
    const { forceReadContractParams } = IdentityStore.contractInfo;
    this.setState({ showContractBtn: !contractChecked, clickNextBtn: isClickNextBtn });

    // 点击勾选框触发弹窗、有强读协议且未阅读过协议，则仅展示强读协议
    if (isClickCheckbox && forceReadContractParams.length > 0 && !hasCountDown) {
      this.setState({ onlyShowForceReadContracts: true });
    } else {
      this.setState({ onlyShowForceReadContracts: false });
    }
    
    // 表单校验通过，弹出协议预览弹窗
    this.setState({ showContractsModal: true });
  }

  /**
   * 关闭协议预览弹窗
   */
  closeContractsModal = () => {
    this.setState({ showContractsModal: false });
  }

  /**
   * 提交表单
   */
  onSubmit = async () => {
    // 1. 协议校验
    if (!this.state.contractChecked) {
      // 弹出协议预览
      this.onContractClick(false, true);
      return;
    }

    // 2. 调用领域服务提交表单（包含表单校验逻辑）
    await IdentityStore.submitForm();
  }

  handleCheckboxClick = () => {
    const isFormValid = IdentityStore.checkForm();
    if (!isFormValid) {
      return;
    }
    const { contractChecked, hasCountDown } = this.state;
    const { forceReadContractParams } = IdentityStore.contractInfo;
    if (contractChecked) {
      this.setState({ contractChecked: false });
      return;
    }
    // 点击勾选时，如果没有强制阅读协议或已读，则直接勾选，预览不需要倒计时
    if (forceReadContractParams.length === 0 || hasCountDown) {
      this.setState({ contractChecked: true });
      return;
    }
    this.onContractClick(true);
  }

  submitAgreement = async () => {
    this.setState({ contractChecked: true, hasCountDown: true });
    // 点击下一步按钮方式触发弹窗，同意后继续下一步逻辑
    if (this.state.clickNextBtn) {
      // 提交表单
      await IdentityStore.submitForm();
    }
  }

  render() {
    const { showContractsModal, contractChecked, hasCountDown, onlyShowForceReadContracts } = this.state;
    const { contractInfo } = IdentityStore;
    const { contractParams, forceReadContractParams } = contractInfo;

    return (
      <MUView className="identity3">
        {/* 身份信息表单 */}
        <MUView>
          {/* 基本身份信息 */}
          <MUView className="identity3-title brand-bg-before">
            身份信息
            <MUView className="info-intro">
              <MUImage src={introImg} className="info-intro-img" />
            </MUView>
          </MUView>

          <MUView className="identity3-form">
            <MUForm className="identity3-form_input">
              {/* 姓名输入 */}
              <MUInput
                name="custName"
                title="姓名"
                type="text"
                placeholder="请输入你的真实姓名"
                clear
                enableBlurTrim
                maxLength={50}
                value={IdentityStore.formPersonalInfo.custName}
                onChange={(val) => IdentityStore.updateForm('custName', val)}
              />

              {/* 身份证号输入 */}
              <MUInput
                name="certId"
                title="身份证号"
                type="idcard"
                placeholder="同意并输入你的身份证号"
                className="identity3-certId"
                clear
                value={IdentityStore.formPersonalInfo.certId}
                enableFormative
                postFixIcon="scan2"
                needCloseKbOnPostFixIconClick
                onChange={(val) => IdentityStore.updateForm('certId', val)}
                onBlur={() => {
                  // 失去焦点时触发完整验证
                  IdentityStore.validateCertId();
                }}
              />
            </MUForm>

            {/* OCR快捷填写 */}
            {/* <MUView className="identity3-form_scan">
              <MUImage className="identity3-form_scan--img" src={scanIdImg} />
              <MUText className="identity3-form_scan--text">快捷填写</MUText>
            </MUView> */}
          </MUView>

          {/* 身份选择器 */}
          <MUView style="margin-top: 10px">
            <IdentityChoice
              formData={{ custTypeSelected: IdentityStore.formPersonalInfo.custTypeSelected }}
              updateForm={(val: string) => IdentityStore.updateForm('custTypeSelected', val)}
            />
          </MUView>

          {/* 职业信息 */}
          <MUView>
            <MUView className="identity3-title brand-bg-before">
              职业信息
            </MUView>
            <MUView className="identity3-form">
              <MUForm className="identity3-form_input">
                <PopupPicker
                  title="职业"
                  placeholder="请选择你的职业"
                  labelFieldName="value"
                  valueFieldName="key"
                  selectorTitle="请选择你的职业类别"
                  ifSingleRow
                  range={dataPool.careerTypeList}
                  value={IdentityStore.formPersonalInfo.careerType}
                  onClickItem={(val: string) => IdentityStore.updateForm('careerType', val)}
                />
              </MUForm>
            </MUView>
          </MUView>

          {/* 月收入 */}
          <MUView>
            <MUView className="identity3-title brand-bg-before">
              月收入
              <MUView className="info-intro">
                <MUImage src={introImg} className="info-intro-img" />
              </MUView>
            </MUView>
            <MUForm className="identity3-form_input">
              <ClickSelector
                title=""
                type="IncomeRange"
                count={3}
                ifShowIcon
                range={dataPool.incomeRangeList}
                value={IdentityStore.formPersonalInfo.incomeRange}
                onClickItem={(val: string) => IdentityStore.updateForm('incomeRange', val)}
              />
            </MUForm>
          </MUView>

          {/* 学校信息 */}
          <SchoolInfo
            formData={{
              highestDegree: IdentityStore.formPersonalInfo.highestDegree,
              schoolName: IdentityStore.formPersonalInfo.schoolName,
              graduateYear: IdentityStore.formPersonalInfo.graduateYear
            }}
            studentDegreeList={dataPool.studentDegreeList}
            updateForm={(key: any, val: string) => IdentityStore.updateForm(key, val)}
          />
        </MUView>

        {/* 协议和提交按钮区域 */}
        <MUView className="contract-btn_block">
          {/* 协议勾选 */}
          <ContractChecker
            contractText={contractInfo.contractText}
            contracts={[contractInfo.contractCheckItem]}
            checkedValue={contractChecked}
            beforeContractText="同意"
            outerControl
            onContractClick={this.onContractClick}
            handleCheckboxClick={this.handleCheckboxClick}
          />

          {/* 协议预览弹窗 */}
          {showContractsModal && (
            <AgreementDrawer
              agreementViewProps={{
                list: onlyShowForceReadContracts ? forceReadContractParams : contractParams,
                current: 0
              }}
              submit={this.submitAgreement}
              show={showContractsModal}
              close={this.closeContractsModal}
              totalCount={!hasCountDown ? contractInfo.forceReadDuration : 0}
            />
          )}

          {/* 提交按钮 */}
          <MUView className="button-block">
            <MUButton
              type="primary"
              className=""
              onClick={this.onSubmit}
            >
              下一步
            </MUButton>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}

export default IdentityPage; 