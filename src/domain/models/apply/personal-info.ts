import { observable, action } from 'mobx';
import { PersonalInfo } from '@types/apply';

/**
 * 个人信息值对象
 */
export class PersonalInfoModel implements PersonalInfo {
  /**
   * 用户名
   */
  @observable custName = '';

  /**
   * 身份证号
   */
  @observable certId = '';

  /**
   * 手机号
   */
  @observable custMobile = '';

  /**
   * 用户身份
   */
  @observable custTypeSelected = '';

  /**
   * 学历
   */
  @observable highestDegree = '';

  /**
   * 学校名称
   */
  @observable schoolName = '';

  /**
   * 学校编号
   */
  @observable schoolCode = '';

  /**
   * 入学年份
   */
  @observable enrollYear = '';

  /**
   * 毕业年份
   */
  @observable graduateYear = '';

  /**
   * 职业
   */
  @observable careerType = '';

  /**
   * 收入范围
   */
  @observable incomeRange = '';

  /**
   * 批量设置个人信息
   * @param data 个人信息数据（支持部分更新）
   */
  @action.bound
  setPersonalInfo = (data: Partial<PersonalInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }

  /**
   * 更新个人信息属性
   * @param key 属性名
   * @param value 属性值
   */
  @action.bound
  updatePersonalInfoProperty(key: keyof PersonalInfo, value: any) {
    this[key] = value;
  }

   /**
    * 组装提交参数
    * 收集表单数据并组装成提交格式
    * @returns 提交参数对象
    */
   @action.bound
   assembleSubmitParams(): any {
    const {
      custName,
      certId,
      custTypeSelected,
      careerType,
      incomeRange,
      highestDegree,
      schoolName,
      graduateYear
    } = this;
    
    // 组装提交参数
    const submitParams = {
      applyPersonalInfo: {
        custName,
        certId,
        custTypeSelected
      },
      applyCompanyInfo: {
        careerType,
        incomeRange,
      },
      applyEducationInfo: {
        highestDegree,
        schoolName,
        graduateYear,
      },
    };
    
    console.log('📋 [FormValidationService] 组装提交参数:', submitParams);
    return submitParams;
  }
}

// 表单数据实例和原始数据实例
let formPersonalInfoInstance: PersonalInfoModel | null = null;
let originalPersonalInfoInstance: PersonalInfoModel | null = null;

/**
 * 获取表单个人信息实例
 */
export const getFormPersonalInfo = (): PersonalInfoModel => {
  if (!formPersonalInfoInstance) {
    formPersonalInfoInstance = new PersonalInfoModel();
  }
  return formPersonalInfoInstance;
};

/**
 * 获取原始个人信息实例
 */
export const getOriginalPersonalInfo = (): PersonalInfoModel => {
  if (!originalPersonalInfoInstance) {
    originalPersonalInfoInstance = new PersonalInfoModel();
  }
  return originalPersonalInfoInstance;
};