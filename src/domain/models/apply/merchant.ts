import { observable, action } from 'mobx';
import { MerchantInfo } from '@types/apply';

/**
 * 商户机构值对象
 */
export class MerchantModel implements MerchantInfo {
  /**
   * 商户号
   */
  @observable merchantId = '';

  /**
   * 商户名
   */
  @observable merchantName = '';

  /**
   * 商户手机号
   */
  @observable merchantMobile = '';

  /**
   * 机构类型
   */
  @observable partnerType = '';

  /**
   * 机构编号
   */
  @observable partnerId = '';

  /**
   * 机构名称
   */
  @observable partner = '';

  /**
   * 机构编号
   */
  @observable orgId = '';

  /**
   * 机构名称
   */
  @observable orgName = '';

  /**
   * 联系方式
   */
  @observable custservicePhoneNo = '';

  /**
   * 批量设置商户机构信息
   * @param data 商户机构数据（支持部分更新）
   */
  @action.bound
  setMerchantInfo = (data: Partial<MerchantInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 商户机构单例实例
let merchantInstance: MerchantModel | null = null;

/**
 * 获取商户机构单例实例
 * @returns MerchantModel实例 - 全局唯一的商户机构实例
 */
export const getMerchantInstance = (): MerchantModel => {
  if (!merchantInstance) {
    merchantInstance = new MerchantModel();
  }
  return merchantInstance;
}; 