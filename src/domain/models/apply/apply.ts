import { observable, action } from 'mobx';
import { ApplyInfo } from '@types/apply';
import { PersonalInfoModel, getFormPersonalInfo } from './personal-info';
import { MerchantModel, getMerchantInstance } from './merchant';
import { IdentityCardModel, getIdentityCardInstance } from './identity-card';

/**
 * 申请实体（聚合根）
 */
export class ApplyModel implements ApplyInfo {
  /**
   * 案件编号
   */
  @observable mainApplyNo = '';

  /**
   * 申请步骤
   */
  @observable applyStep = '';

  /**
   * 拒绝码
   */
  @observable rejectCode = '';

  /**
   * 申请结果副标题文案
   */
  @observable channelControlDesc = '';

  /**
   * 激活渠道是否是自有渠道
   */
  @observable activateChannelFlag = '';

  /**
   * 当前渠道是否支持激活
   */
  @observable curChannelSupport = '';

  /**
   * 个人信息值对象
   */
  @observable personalInfo: PersonalInfoModel;

  /**
   * 商户机构值对象
   */
  @observable merchant: MerchantModel;

  /**
   * 身份信息页卡片属性值对象
   */
  @observable identityCard: IdentityCardModel;

  constructor() {
    this.personalInfo = getFormPersonalInfo();
    this.merchant = getMerchantInstance();
    this.identityCard = getIdentityCardInstance();
  }

  /**
   * 批量设置申请信息
   * @param data 申请数据（支持部分更新）
   */
  @action.bound
  setApplyInfo = (data: Partial<ApplyInfo>) => {
    const valueObjectKeys = ['personalInfo', 'merchant', 'identityCard'];
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k] && !valueObjectKeys.includes(k)) {
        this[k] = data[k];
      }
    });
  }
}

// 申请聚合单例实例
let applyInstance: ApplyModel | null = null;

/**
 * 获取申请聚合单例实例
 * @returns ApplyModel实例 - 全局唯一的申请聚合实例
 */
export const getApplyInstance = (): ApplyModel => {
  if (!applyInstance) {
    applyInstance = new ApplyModel();
  }
  return applyInstance;
}; 