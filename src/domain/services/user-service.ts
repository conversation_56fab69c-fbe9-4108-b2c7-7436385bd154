
import { getUserInfo, getAvatar } from '@api/user';
import { UserInfo } from '@types/user.d';
import { getUser } from '../models/user/user';

/**
 * 用户领域服务类 - DDD 领域层服务
 *
 * 领域服务封装不适合放在实体或值对象中的领域逻辑。
 * 用户服务负责协调用户相关的复杂业务操作，包括：
 *
 * 职责范围：
 * 1. 协调多个外部资源（API调用）
 * 2. 处理用户信息获取和更新的业务流程
 * 3. 确保用户数据的一致性和完整性
 * 4. 提供高层次的用户操作接口
 *
 * 设计原则：
 * - 无状态：不持有自己的数据状态
 * - 纯函数：每次调用结果仅依赖于输入参数
 * - 单一职责：专注于用户相关的业务逻辑
 *
 * @example
 * ```typescript
 * // 获取用户信息
 * const user = await UserService.getUserInfo();
 *
 * // 获取用户头像
 * const userWithAvatar = await UserService.getUserAvatar('avatar123');
 * ```
 */
export class UserService {
  /**
   * 获取用户信息并更新用户模型
   *
   * 这是一个复合业务操作，包含：
   * 1. 调用数据接口层获取用户信息
   * 2. 通过用户实体更新状态
   * 3. 返回更新后的用户模型
   *
   * 业务流程：
   * API调用 -> 数据转换 -> 实体更新 -> 返回结果
   *
   * @returns 更新后的用户模型
   * @throws 当API调用失败时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const user = await UserService.getUserInfo();
   *   console.log('用户登录状态:', user.isLogin);
   * } catch (error) {
   *   console.error('获取用户信息失败:', error);
   * }
   * ```
   */
  static async getUserInfo(): Promise<UserInfo> {
    const userInfo = await getUserInfo();
    const user = getUser();
    user.setState(userInfo);
    return user;
  }

  /**
   * 获取用户头像并更新用户模型
   *
   * 根据头像ID获取用户头像数据，并更新到用户实体中。
   * 如果没有提供头像ID，则直接返回当前用户实例。
   *
   * 业务逻辑：
   * 1. 验证头像ID是否存在
   * 2. 调用API获取头像数据
   * 3. 更新用户实体的头像信息
   * 4. 返回更新后的用户模型
   *
   * @param avatarId 头像ID，可选参数
   * @returns 更新后的用户模型
   *
   * @example
   * ```typescript
   * // 获取指定头像
   * const user = await UserService.getUserAvatar('avatar123');
   *
   * // 不提供头像ID，返回当前用户
   * const currentUser = await UserService.getUserAvatar();
   * ```
   */
  static async getUserAvatar(avatarId?: string): Promise<UserInfo> {
    if (avatarId) {
      const avatarInfo = await getAvatar(avatarId);
      const user = getUser();
      user.setState(avatarInfo);
      return user;
    }

    return getUser();
  }
}