// 表单验证领域服务 
import Madp from '@mu/madp';
import { Validator } from '@mu/madp-utils';
import { PersonalInfo } from '@types/apply';
import { getFormPersonalInfo, getOriginalPersonalInfo } from '../models/apply/personal-info';
import { checkName, getCertIdAge } from '@utils';

export class FormValidationService {
  /**
   * 更新表单个人信息属性
   * @param key 属性名
   * @param value 属性值
   */
  static updateFormProperty(key: keyof PersonalInfo, value: any) {
    const formPersonalInfo = getFormPersonalInfo();
    formPersonalInfo.updatePersonalInfoProperty(key, value);
    // 身份证号特殊处理逻辑
    if (key === 'certId' && value.length === 18) {
      this.checkCertIdFormat();
    }
  }

  /**
   * 校验身份证号格式是否正确
   * @param certId 身份证号
   * @returns 是否为合法身份证号
   */
  static checkCertIdFormat(): boolean {
    const formPersonalInfo = getFormPersonalInfo();
    const originalPersonalInfo = getOriginalPersonalInfo();
    const { certId } = formPersonalInfo || {};
    // 检查是否为空
    if (!certId) {
      Madp.showToast({
        icon: 'none',
        title: '请输入你的身份证号',
      });
      return false;
    }
    if (originalPersonalInfo.certId !== certId && !Validator.isIdentityNumber(certId)) {
      Madp.showToast({
        icon: 'none',
        title: '请填写正确的身份号',
      });
      return false;
    }
    return true;
  }

  /**
   * 表单完整性校验
   * @returns 校验是否通过
   */
  static checkFormData(): boolean {
    const formPersonalInfo = getFormPersonalInfo();
    const originalPersonalInfo = getOriginalPersonalInfo();
    // 定义字段校验规则
    const validationRules = [
      {
        key: 'custName',
        value: formPersonalInfo.custName,
        message: '请输入你的姓名'
      },
      {
        key: 'certId',
        value: formPersonalInfo.certId,
        message: '请输入你的身份证号'
      },
      {
        key: 'custTypeSelected',
        value: formPersonalInfo.custTypeSelected,
        message: '请选择你的身份'
      },
      {
        key: 'careerType',
        value: formPersonalInfo.careerType,
        message: '请选择你的职业'
      },
      {
        key: 'incomeRange',
        value: formPersonalInfo.incomeRange,
        message: '请选择你的收入范围'
      },
      {
        key: 'highestDegree',
        value: formPersonalInfo.highestDegree,
        message: '请选择你的学历'
      },
      {
        key: 'schoolName',
        value: formPersonalInfo.schoolName,
        message: '请输入你的学校名称'
      },
      {
        key: 'graduateYear',
        value: formPersonalInfo.graduateYear,
        message: '请选择你的毕业年份'
      }
    ];

    // 非空校验
    for (const rule of validationRules) {
      if (!rule.value || rule.value.trim() === '') {
        Madp.showToast({
          icon: 'none',
          title: rule.message,
        });
        return false;
      }
    }

    // 姓名校验
    if (originalPersonalInfo.custName !== formPersonalInfo.custName && checkName(formPersonalInfo.custName)) {
      Madp.showToast({
        icon: 'none',
        title: '填写的姓名不正确，请重新填写！',
      });
      return false;
    }

    const certId = formPersonalInfo.certId;
    if (originalPersonalInfo.certId !== certId) {
      // 身份证号格式校验
      if (!Validator.isIdentityNumber(certId)) {
        Madp.showToast({
          icon: 'none',
          title: '请填写正确的身份号',
        });
        return false;
      }
      // 身份证号年龄校验
      const age = getCertIdAge(certId);
      if (age < 18 || age > 60) {
        Madp.showToast({
          icon: 'none',
          title: '很抱歉，你的年龄暂不符合申请要求',
        });
        return false;
      }
    }

    return true;
  }

  /**
  * 提交表单
  * 集成表单校验和提交逻辑
  * @returns 提交是否成功
  */
  static async submitForm(): Promise<boolean> {
    const formPersonalInfo = getFormPersonalInfo();
    // 执行表单校验
    if (!this.checkFormData()) {
      return false;
    }
    
    try {
      // 组装提交参数
      const submitParams = formPersonalInfo.assembleSubmitParams();
      
      // TODO: 实际调用API提交
      // const result = await submitApplyInfo(submitParams);
      const result = await Promise.resolve({ success: true, message: '提交成功' });
      
      console.log('✅ [FormValidationService] 提交成功:', result);
      Madp.showToast({
        icon: 'none',
        title: '提交成功',
      });
      return true;
    } catch (error) {
      return false;
    }
  }
 } 