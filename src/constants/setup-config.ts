// 传入渠道基础SDK setup方法的配置对象

const pidMaps = {
  // eslint-disable-next-line camelcase
  all_prod: 'brtgwl5o94@15d57728c83c1e6', // 写入生产的pid
  // eslint-disable-next-line camelcase
  all_dev: 'brtgwl5o94@60985b5578ae703', // 写入测试的pid
  // eslint-disable-next-line camelcase
  offline_prod: 'brtgwl5o94@15d57728c83c1e6', // 写入生产的pid_离线包
  // eslint-disable-next-line camelcase
  offline_dev: 'brtgwl5o94@60985b5578ae703', // 写入测试的pid_离线包
  '/pages/index/index_prod': 'brtgwl5o94@15d57728c83c1e6',
  '/index_prod': 'brtgwl5o94@15d57728c83c1e6',
  '/pages/index/index_dev': 'brtgwl5o94@60985b5578ae703',
  '/index_dev': 'brtgwl5o94@60985b5578ae703',
  debug: true, // 是否开启开启渠道SDK日志入参，true为开启，false（默认值）-不开启
};

export default {
  constants: {
    pidMaps,
  },
};
