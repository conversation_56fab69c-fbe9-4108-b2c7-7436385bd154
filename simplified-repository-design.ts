// ============================================================================
// 简化的仓储设计 - 接口和实现都在 domain/repositories 目录下
// ============================================================================

// ============================================================================
// 简化后的目录结构
// ============================================================================

/*
src/
├── domain/                          # 领域层
│   ├── entities/                    # 实体
│   │   ├── contract/
│   │   │   ├── contract.entity.ts
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── value-objects/               # 值对象
│   │   ├── contract/
│   │   │   ├── contract-id.vo.ts
│   │   │   ├── contract-type.vo.ts
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── services/                    # 领域服务
│   │   ├── contract-template.service.ts
│   │   └── index.ts
│   └── repositories/                # 仓储（接口+实现）
│       ├── contract.repository.ts   # 合同仓储
│       ├── personal-info.repository.ts
│       └── index.ts
├── application/                     # 应用层
│   ├── services/
│   │   ├── contract-application.service.ts
│   │   └── index.ts
│   └── dto/
│       ├── contract.dto.ts
│       └── index.ts
├── infrastructure/                  # 基础设施层（只保留API适配器）
│   ├── api/
│   │   ├── contract/
│   │   │   ├── contract.api.ts
│   │   │   └── contract.translator.ts
│   │   └── index.ts
│   └── cache/
│       ├── cache.service.ts
│       └── index.ts
└── presentation/                    # 表示层
    ├── stores/
    │   ├── contract-ui.store.ts
    │   └── index.ts
    └── pages/
        ├── identity/
        └── index.ts
*/

// ============================================================================
// domain/repositories/contract.repository.ts - 合同仓储（接口+实现一体）
// ============================================================================

import { Contract } from '../entities/contract/contract.entity';
import { ContractId } from '../value-objects/contract/contract-id.vo';
import { ContractType } from '../value-objects/contract/contract-type.vo';
import { ForceReadConfig } from '../value-objects/contract/force-read-config.vo';

/**
 * 合同仓储类 - 直接包含接口定义和实现
 * 
 * 优点：
 * - 结构简单，易于理解和维护
 * - 减少了接口和实现分离的复杂性
 * - 适合中小型项目快速开发
 * 
 * 注意：
 * - 仍然封装了数据访问逻辑
 * - 提供领域特有的查询方法
 * - 负责数据转换和错误处理
 */
export class ContractRepository {
  constructor(
    private readonly apiClient: ApiClient,
    private readonly cacheService: CacheService
  ) {}

  // ============================================================================
  // 基础 CRUD 操作
  // ============================================================================

  /**
   * 根据ID查找合同
   */
  async findById(id: ContractId): Promise<Contract | null> {
    try {
      // 1. 先从缓存查找
      const cacheKey = `contract:${id.getValue()}`;
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return this.toDomainEntity(cached);
      }

      // 2. 从API获取
      const response = await this.apiClient.get(`/contracts/${id.getValue()}`);
      if (!response.data) {
        return null;
      }

      // 3. 转换并缓存
      const contract = this.toDomainEntity(response.data);
      if (contract) {
        await this.cacheService.set(cacheKey, response.data, 300);
      }

      return contract;
    } catch (error) {
      console.error('Failed to find contract by id:', error);
      return null;
    }
  }

  /**
   * 保存合同
   */
  async save(contract: Contract): Promise<Contract> {
    try {
      const apiData = this.toApiData(contract);
      const response = await this.apiClient.post('/contracts', apiData);
      
      // 清除相关缓存
      await this.clearRelatedCache(contract);
      
      return this.toDomainEntity(response.data);
    } catch (error) {
      console.error('Failed to save contract:', error);
      throw new Error('合同保存失败');
    }
  }

  /**
   * 删除合同
   */
  async delete(id: ContractId): Promise<void> {
    try {
      await this.apiClient.delete(`/contracts/${id.getValue()}`);
      await this.cacheService.delete(`contract:${id.getValue()}`);
    } catch (error) {
      console.error('Failed to delete contract:', error);
      throw new Error('合同删除失败');
    }
  }

  // ============================================================================
  // 领域特有查询方法
  // ============================================================================

  /**
   * 根据合同组ID查找合同列表
   */
  async findByGroupId(groupId: string): Promise<Contract[]> {
    try {
      const cacheKey = `contracts:group:${groupId}`;
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return cached.map(item => this.toDomainEntity(item)).filter(Boolean);
      }

      const response = await this.apiClient.get('/contracts', {
        params: { contractGroupId: groupId, status: 'ACTIVE' }
      });

      if (!response.data?.contracts) {
        return [];
      }

      // 缓存原始数据
      await this.cacheService.set(cacheKey, response.data.contracts, 180);

      return response.data.contracts
        .map(item => this.toDomainEntity(item))
        .filter(contract => contract !== null);

    } catch (error) {
      console.error('Failed to find contracts by group id:', error);
      return [];
    }
  }

  /**
   * 查找需要强制阅读的合同
   */
  async findForceReadContracts(groupId: string): Promise<Contract[]> {
    const allContracts = await this.findByGroupId(groupId);
    return allContracts.filter(contract => contract.isForceReadRequired());
  }

  /**
   * 根据商户和渠道查找适用的合同
   */
  async findByMerchantAndChannel(merchantId: string, channelType: string): Promise<Contract[]> {
    try {
      // 根据业务规则确定合同组
      const contractGroupId = this.determineContractGroup(merchantId, channelType);
      return await this.findByGroupId(contractGroupId);
    } catch (error) {
      console.error('Failed to find contracts by merchant and channel:', error);
      return [];
    }
  }

  /**
   * 根据合同类型查找合同
   */
  async findByType(contractType: ContractType): Promise<Contract[]> {
    try {
      const response = await this.apiClient.get('/contracts', {
        params: { contractType: contractType.getValue() }
      });

      if (!response.data?.contracts) {
        return [];
      }

      return response.data.contracts
        .map(item => this.toDomainEntity(item))
        .filter(contract => contract !== null);

    } catch (error) {
      console.error('Failed to find contracts by type:', error);
      return [];
    }
  }

  /**
   * 批量查找合同
   */
  async findByIds(ids: ContractId[]): Promise<Contract[]> {
    const promises = ids.map(id => this.findById(id));
    const results = await Promise.all(promises);
    return results.filter(contract => contract !== null);
  }

  // ============================================================================
  // 私有辅助方法
  // ============================================================================

  /**
   * API数据转换为领域实体
   */
  private toDomainEntity(apiData: any): Contract | null {
    try {
      if (!apiData || !apiData.contractId) {
        return null;
      }

      const contractId = new ContractId(apiData.contractId);
      const contractType = new ContractType(apiData.contractType);
      const forceReadConfig = new ForceReadConfig(
        apiData.forceReadFlag,
        parseInt(apiData.forceReadDuration) || 0
      );

      return Contract.create({
        id: contractId,
        type: contractType,
        groupId: apiData.contractGroupId,
        text: apiData.contractText,
        version: apiData.contractVersion,
        forceReadConfig: forceReadConfig,
        isOldContract: !apiData.contractCode
      });

    } catch (error) {
      console.error('Failed to convert API data to domain entity:', error);
      return null;
    }
  }

  /**
   * 领域实体转换为API数据
   */
  private toApiData(contract: Contract): any {
    return {
      contractId: contract.getId().getValue(),
      contractType: contract.getType().getValue(),
      contractGroupId: contract.getGroupId(),
      contractText: contract.getText(),
      contractVersion: contract.getVersion(),
      forceReadFlag: contract.getForceReadConfig().isRequired() ? 'Y' : 'N',
      forceReadDuration: contract.getForceReadConfig().getDuration().toString(),
    };
  }

  /**
   * 根据商户和渠道确定合同组
   */
  private determineContractGroup(merchantId: string, channelType: string): string {
    // 自营渠道使用标准协议
    if (merchantId === '10000' || merchantId === '10001') {
      return 'XYDC';
    }
    // 非自营渠道使用非自营协议
    return 'XYDCFZY';
  }

  /**
   * 清除相关缓存
   */
  private async clearRelatedCache(contract: Contract): Promise<void> {
    const patterns = [
      `contract:${contract.getId().getValue()}`,
      `contracts:group:${contract.getGroupId()}`,
      'contracts:force-read:*'
    ];

    await Promise.all(
      patterns.map(pattern => this.cacheService.deletePattern(pattern))
    );
  }
}

// ============================================================================
// domain/repositories/personal-info.repository.ts - 个人信息仓储
// ============================================================================

export class PersonalInfoRepository {
  constructor(
    private readonly apiClient: ApiClient,
    private readonly cacheService: CacheService
  ) {}

  async getCurrent(): Promise<PersonalInfo | null> {
    try {
      const cacheKey = 'personal-info:current';
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return PersonalInfo.fromData(cached);
      }

      const response = await this.apiClient.get('/personal-info/current');
      if (!response.data) {
        return null;
      }

      const personalInfo = PersonalInfo.fromData(response.data);
      await this.cacheService.set(cacheKey, response.data, 600);

      return personalInfo;
    } catch (error) {
      console.error('Failed to get current personal info:', error);
      return null;
    }
  }

  async save(personalInfo: PersonalInfo): Promise<PersonalInfo> {
    try {
      const apiData = personalInfo.toApiData();
      const response = await this.apiClient.post('/personal-info', apiData);
      
      // 清除缓存
      await this.cacheService.delete('personal-info:current');
      
      return PersonalInfo.fromData(response.data);
    } catch (error) {
      console.error('Failed to save personal info:', error);
      throw new Error('个人信息保存失败');
    }
  }
}

// ============================================================================
// domain/repositories/index.ts - 仓储导出
// ============================================================================

export { ContractRepository } from './contract.repository';
export { PersonalInfoRepository } from './personal-info.repository';

// 仓储工厂 - 简化依赖注入
export class RepositoryFactory {
  private static contractRepository: ContractRepository;
  private static personalInfoRepository: PersonalInfoRepository;

  static getContractRepository(): ContractRepository {
    if (!this.contractRepository) {
      this.contractRepository = new ContractRepository(
        new ApiClient(),
        new CacheService()
      );
    }
    return this.contractRepository;
  }

  static getPersonalInfoRepository(): PersonalInfoRepository {
    if (!this.personalInfoRepository) {
      this.personalInfoRepository = new PersonalInfoRepository(
        new ApiClient(),
        new CacheService()
      );
    }
    return this.personalInfoRepository;
  }
}
