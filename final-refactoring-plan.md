# 基于实际目录结构的DDD重构方案

## 重构目标

基于您提供的实际目录结构，在保持现有架构基础上，通过在 `domain` 下新增 `repositories` 目录，解决当前 contract model 的DDD违规问题。

## 目录结构（保持不变 + 新增repositories）

```
src/
├── domain/
│   ├── models/                      # 现有：领域模型
│   │   ├── apply/
│   │   │   ├── apply.ts            # 聚合根
│   │   │   ├── identity-card.ts    # 实体
│   │   │   ├── merchant.vo.ts      # 值对象
│   │   │   └── personal-info.vo.ts
│   │   ├── contract/
│   │   │   └── contract.ts         # 需要重构
│   │   └── user/
│   │       └── user.ts
│   ├── repositories/               # 新增：仓储层
│   │   ├── apply.repository.ts
│   │   ├── contract.repository.ts
│   │   └── user.repository.ts
│   └── services/                   # 现有：领域服务
│       ├── apply-service.ts
│       ├── contract-service.ts     # 需要重构
│       ├── form-validation-service.ts
│       └── user-service.ts
├── pages/                          # 现有：表现层
│   ├── identity/
│   │   ├── store.ts               # 需要重构：分离UI状态
│   │   └── index.tsx              # 需要重构：简化组件
│   └── ...
└── ... (其他目录保持不变)
```

## 核心重构内容

### 1. **Contract 领域模型重构**

#### **重构前问题**
```typescript
// ❌ 当前的问题
export class ContractModel implements ContractInfo {
  @observable forceReadFlag = '';           // 业务属性
  @observable isShowContract = false;       // UI状态 ❌
  @observable contractParams: any[] = [];   // UI数据 ❌
  @observable contractCheckItem: any = {};  // UI状态 ❌
  
  @action.bound
  setContractInfo = (data: Partial<ContractInfo>) => { /* ... */ }
}
```

#### **重构后设计**
```typescript
// ✅ 纯领域模型
export class Contract {
  private constructor(
    private readonly id: string,
    private readonly type: string,
    private readonly groupId: string,
    private readonly text: string,
    private readonly forceReadFlag: string,
    private readonly forceReadDuration: number,
    private readonly isOldContract: boolean
  ) {}

  // 工厂方法
  static create(props: ContractProps): Contract { /* ... */ }
  
  // 业务方法
  isForceReadRequired(): boolean { return this.forceReadFlag === 'Y'; }
  getForceReadDuration(): number { return this.forceReadDuration; }
  
  // 访问器
  getId(): string { return this.id; }
  getType(): string { return this.type; }
  // ...
}
```

### 2. **新增仓储层**

#### **ContractRepository**
```typescript
// domain/repositories/contract.repository.ts
export class ContractRepository {
  /**
   * 根据合同组ID获取合同配置
   */
  async findByGroupId(groupId: string): Promise<Contract[]> {
    // 调用现有API，转换为领域对象
    const contractConfig = await queryContractConfig(groupId);
    return contractConfig.contracts.map(item => Contract.create({
      id: item.id,
      type: item.contractType,
      groupId: groupId,
      text: item.text,
      forceReadFlag: item.forceReadFlag,
      forceReadDuration: parseInt(item.forceReadDuration) || 0,
      isOldContract: !item.contractCode
    }));
  }

  /**
   * 获取强制阅读合同
   */
  async findForceReadContracts(groupId: string): Promise<Contract[]> {
    const contracts = await this.findByGroupId(groupId);
    return contracts.filter(contract => contract.isForceReadRequired());
  }

  /**
   * 获取合同模板
   */
  async getContractTemplate(params: any, title: string): Promise<any> {
    const htmlFile = await queryContractTemplate(params);
    return { title, htmlFile };
  }
}
```

### 3. **重构领域服务**

#### **ContractService 重构**
```typescript
// domain/services/contract-service.ts
export class ContractService {
  private contractRepository: ContractRepository;

  constructor() {
    this.contractRepository = new ContractRepository();
  }

  /**
   * 获取合同参数 - 重构后专注业务逻辑
   */
  async getContractParams(): Promise<void> {
    const contractModel = getContractInstance();
    const { isShowContract, oldContractFlag } = contractModel;
    
    if (isShowContract) {
      if (oldContractFlag) {
        await this.getOldContractParams();
      } else {
        await this.getNewContractParams();
      }
    }
  }

  private async getOldContractParams() {
    // 使用仓储获取合同数据
    const contracts = await this.contractRepository.findByMerchantInfo(merchantId);
    // 业务逻辑处理...
  }
}
```

### 4. **页面Store重构**

#### **分离UI状态管理**
```typescript
// pages/identity/store.ts
class IdentityStore {
  // ============================================================================
  // 领域模型引用（保持兼容性）
  // ============================================================================
  @observable formPersonalInfo = getFormPersonalInfo();
  @observable merchant = getMerchantInstance();
  @observable identityCard = getIdentityCardInstance();
  @observable contractInfo = getContractInstance();

  // ============================================================================
  // UI状态管理（新增）
  // ============================================================================
  @observable showContractsModal = false;
  @observable contractChecked = false;
  @observable hasCountDown = false;
  @observable onlyShowForceReadContracts = false;
  @observable isLoading = false;
  @observable error: string | null = null;

  // ============================================================================
  // 领域服务实例
  // ============================================================================
  private contractService: ContractService;

  constructor() {
    this.contractService = new ContractService();
  }

  // UI状态管理方法
  @action.bound
  showContractModal = (onlyShowForceRead = false) => {
    this.onlyShowForceReadContracts = onlyShowForceRead;
    this.showContractsModal = true;
  }

  @action.bound
  handleContractClick = (isClickCheckbox = false) => {
    const isFormValid = this.checkForm();
    if (!isFormValid) return;

    const { forceReadContractParams } = this.contractInfo;
    if (isClickCheckbox && forceReadContractParams.length > 0 && !this.hasCountDown) {
      this.showContractModal(true);
    } else {
      this.showContractModal(false);
    }
  }
}
```

### 5. **页面组件简化**

#### **重构后的页面组件**
```typescript
// pages/identity/index.tsx
@observer
class IdentityPage extends Component {
  async componentDidMount() {
    await IdentityStore.initComp();
    await IdentityStore.getContractParams();
  }

  onContractClick = (isClickCheckbox = false, isClickNextBtn = false) => {
    IdentityStore.handleContractClick(isClickCheckbox, isClickNextBtn);
  }

  handleCheckboxClick = () => {
    IdentityStore.handleCheckboxClick();
  }

  render() {
    const {
      formPersonalInfo,
      contractInfo,
      showContractsModal,
      contractChecked,
      hasCountDown,
      onlyShowForceReadContracts
    } = IdentityStore;

    return (
      <MUView className="identity3">
        {/* 表单组件 */}
        <MUInput
          value={formPersonalInfo.custName}
          onChange={(val) => IdentityStore.updateForm('custName', val)}
        />
        
        {/* 协议组件 */}
        <ContractChecker
          contractText={contractInfo.contractText}
          checkedValue={contractChecked}
          onContractClick={this.onContractClick}
          handleCheckboxClick={this.handleCheckboxClick}
        />
        
        {/* 协议弹窗 */}
        {showContractsModal && (
          <AgreementDrawer
            list={onlyShowForceReadContracts ? forceReadContractParams : contractParams}
            show={showContractsModal}
            close={() => IdentityStore.hideContractModal()}
          />
        )}
      </MUView>
    );
  }
}
```

## 重构优势

### ✅ **解决的问题**

1. **领域模型纯净性**
   - 移除UI状态和MobX装饰器
   - Contract成为纯业务对象
   - 符合DDD领域模型设计原则

2. **职责分离**
   - 仓储负责数据访问
   - 领域服务负责业务逻辑编排
   - UI Store负责界面状态管理

3. **可维护性提升**
   - 清晰的分层架构
   - 单一职责原则
   - 易于测试和扩展

### ✅ **保持的优势**

1. **现有架构兼容**
   - 保持现有目录结构
   - 保持现有API调用方式
   - 渐进式重构，风险可控

2. **开发效率**
   - 简化的仓储设计
   - 直接的依赖关系
   - 快速开发和调试

## 迁移计划

### **阶段一：基础重构（2天）**
1. 重构 `Contract` 领域模型，移除UI状态
2. 创建 `ContractRepository` 仓储类
3. 保持现有 `ContractModel` 作为过渡

### **阶段二：服务层重构（1天）**
1. 重构 `ContractService`，使用仓储
2. 保持现有接口不变，内部实现重构

### **阶段三：UI层重构（2天）**
1. 重构 `IdentityStore`，分离UI状态
2. 简化页面组件逻辑
3. 测试UI交互功能

### **阶段四：清理和优化（1天）**
1. 移除过渡代码
2. 完善错误处理
3. 性能优化和测试

## 总结

这个重构方案在保持现有目录结构和开发习惯的基础上，通过新增仓储层和重构领域模型，有效解决了DDD违规问题。主要特点：

- **实用主义**：不过度设计，保持简单有效
- **渐进式重构**：风险可控，可分步实施
- **兼容性好**：保持现有接口，减少影响面
- **职责清晰**：分离业务逻辑和UI状态管理

这种设计既解决了架构问题，又保持了开发效率，是一个平衡的解决方案。
