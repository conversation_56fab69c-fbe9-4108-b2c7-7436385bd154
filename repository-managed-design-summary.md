# 仓储层统一管理实例的设计方案

## 设计理念

将单实例和多实例的维护都放在仓储层，让仓储层成为数据访问和实例管理的统一入口。

## 核心设计

### 1. **仓储层职责扩展**

#### **多实例管理（ContractRepository）**
```typescript
export class ContractRepository {
  @observable private contracts: Map<string, Contract> = new Map();
  @observable private contractsByGroup: Map<string, Contract[]> = new Map();

  // 实例管理
  @action private addContract(contract: Contract): void { /* ... */ }
  @action private addContracts(contracts: Contract[]): void { /* ... */ }
  @action private clearGroup(groupId: string): void { /* ... */ }

  // 数据访问
  async findByGroupId(groupId: string): Promise<Contract[]> { /* ... */ }
  getForceReadContracts(groupId: string): Contract[] { /* ... */ }
  getApplicableContracts(groupId: string, merchantId: string): Contract[] { /* ... */ }

  // 业务方法
  async getContractTemplates(groupId: string, personalInfo: any, merchantInfo: any): Promise<any[]> { /* ... */ }
}
```

#### **单实例管理（UserRepository）**
```typescript
export class UserRepository {
  @observable private userInstance: User | null = null;

  // 单实例管理
  async getCurrentUser(): Promise<User | null> {
    if (this.userInstance) {
      return this.userInstance; // 返回已有实例
    }
    // 从API获取并创建实例
    const userData = await queryCurrentUser();
    this.userInstance = new User(userData);
    return this.userInstance;
  }

  async updateUser(userData: any): Promise<User | null> {
    // 更新实例和API
  }

  @action clearUser(): void {
    this.userInstance = null;
  }
}
```

### 2. **仓储工厂统一管理**

```typescript
export class RepositoryFactory {
  private static contractRepository: ContractRepository | null = null;
  private static userRepository: UserRepository | null = null;

  static getContractRepository(): ContractRepository {
    if (!this.contractRepository) {
      this.contractRepository = new ContractRepository();
    }
    return this.contractRepository;
  }

  static getUserRepository(): UserRepository {
    if (!this.userRepository) {
      this.userRepository = new UserRepository();
    }
    return this.userRepository;
  }
}
```

### 3. **服务层简化为编排**

```typescript
export class ContractService {
  private contractRepository: ContractRepository;

  constructor() {
    this.contractRepository = RepositoryFactory.getContractRepository();
  }

  // 只保留编排逻辑，具体实例管理委托给仓储
  async getContractTemplates(contractGroupId: string, personalInfo: any, merchantInfo: any): Promise<any[]> {
    return await this.contractRepository.getContractTemplates(contractGroupId, personalInfo, merchantInfo);
  }
}
```

### 4. **页面Store专注UI状态**

```typescript
export class IdentityStore {
  // UI状态
  @observable showContractsModal = false;
  @observable contractChecked = false;
  @observable hasCountDown = false;

  // 仓储引用
  private contractRepository: ContractRepository;
  private userRepository: UserRepository;

  constructor() {
    this.contractRepository = RepositoryFactory.getContractRepository();
    this.userRepository = RepositoryFactory.getUserRepository();
  }

  // 计算属性从仓储获取数据
  @computed get currentUser(): User | null {
    return this.userRepository.userInstance;
  }

  @computed get contracts(): Contract[] {
    return this.contractRepository.contractsByGroup.get(this.currentContractGroupId) || [];
  }

  @computed get contractText(): string {
    return this.contracts.map(c => c.getText()).join('、');
  }
}
```

## 设计优势

### ✅ **统一的数据访问**
- 所有数据访问都通过仓储层
- 单实例和多实例管理策略统一
- 缓存和状态管理集中处理

### ✅ **清晰的职责分离**
- **仓储层**：数据访问 + 实例管理 + 缓存
- **服务层**：业务编排 + 跨仓储协调
- **Store层**：UI状态管理 + 响应式计算

### ✅ **响应式数据绑定**
- 仓储层使用MobX管理实例状态
- Store层通过computed获取响应式数据
- 自动更新UI，无需手动触发

### ✅ **灵活的访问方式**
- 可以通过Service访问（编排场景）
- 可以直接访问Repository（简单场景）
- Store提供便捷的计算属性

## 实例管理策略

### **单实例模式（User）**
```typescript
// 特点：全局唯一，状态共享
class UserRepository {
  @observable private userInstance: User | null = null;
  
  async getCurrentUser(): Promise<User | null> {
    if (this.userInstance) return this.userInstance; // 复用实例
    // 创建新实例...
  }
}
```

### **多实例模式（Contract）**
```typescript
// 特点：按组管理，支持集合操作
class ContractRepository {
  @observable private contracts: Map<string, Contract> = new Map();
  @observable private contractsByGroup: Map<string, Contract[]> = new Map();
  
  async findByGroupId(groupId: string): Promise<Contract[]> {
    if (this.contractsByGroup.has(groupId)) {
      return this.contractsByGroup.get(groupId)!; // 复用实例
    }
    // 创建新实例...
  }
}
```

## 数据流向

```
API数据 → 仓储层（实例管理+缓存） → 服务层（编排） → Store层（UI状态+计算属性） → 组件（渲染）
                ↑                                                    ↑
            直接访问（简单场景）                              响应式更新
```

## 使用示例

### **在页面组件中使用**
```typescript
@observer
class IdentityPage extends Component {
  async componentDidMount() {
    // 通过Store初始化
    await IdentityStore.initializePage('XYDC');
  }

  render() {
    const { contractText, currentUser, contracts } = IdentityStore;
    
    return (
      <div>
        <div>用户：{currentUser?.getName()}</div>
        <div>合同：{contractText}</div>
        <div>合同数量：{contracts.length}</div>
      </div>
    );
  }
}
```

### **直接访问仓储**
```typescript
// 在某些场景下可以直接访问仓储
const contractRepo = RepositoryFactory.getContractRepository();
const contracts = await contractRepo.findByGroupId('XYDC');
const forceReadContracts = contractRepo.getForceReadContracts('XYDC');
```

## 迁移步骤

### **阶段一：创建仓储层（1天）**
1. 创建ContractRepository和UserRepository
2. 实现实例管理和数据访问方法
3. 创建RepositoryFactory

### **阶段二：重构服务层（0.5天）**
1. 简化现有Service，移除实例管理逻辑
2. 改为使用仓储进行数据访问

### **阶段三：重构Store层（1天）**
1. 移除领域模型直接引用
2. 改为通过仓储访问数据
3. 添加计算属性保持响应式

### **阶段四：测试和优化（0.5天）**
1. 测试数据绑定和响应式更新
2. 性能优化和缓存策略调整

## 总结

这种设计将实例管理的职责完全交给仓储层，实现了：

1. **统一管理**：单实例和多实例都在仓储层管理
2. **职责清晰**：每层都有明确的职责边界
3. **响应式保持**：通过MobX保持数据绑定
4. **灵活访问**：支持多种访问方式

这是一个更加统一和规范的架构设计，特别适合需要复杂数据管理的应用场景。
