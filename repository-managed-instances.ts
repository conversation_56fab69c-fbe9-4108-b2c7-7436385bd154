// ============================================================================
// 仓储层统一管理单实例和多实例的设计方案
// ============================================================================

import { observable, action, computed } from 'mobx';

// ============================================================================
// 1. 领域模型保持不变（只包含业务逻辑）
// ============================================================================

/**
 * 合同领域模型 - 纯业务逻辑
 */
export class Contract {
  @observable private id: string;
  @observable private type: string;
  @observable private groupId: string;
  @observable private text: string;
  @observable private forceReadFlag: string;
  @observable private forceReadDuration: number;
  @observable private isOldContract: boolean;

  constructor(props: {
    id: string;
    type: string;
    groupId: string;
    text: string;
    forceReadFlag: string;
    forceReadDuration: number;
    isOldContract: boolean;
  }) {
    this.id = props.id;
    this.type = props.type;
    this.groupId = props.groupId;
    this.text = props.text;
    this.forceReadFlag = props.forceReadFlag;
    this.forceReadDuration = props.forceReadDuration;
    this.isOldContract = props.isOldContract;
  }

  // 业务逻辑方法
  @computed get isForceReadRequired(): boolean {
    return this.forceReadFlag === 'Y';
  }

  @computed get readDuration(): number {
    return this.forceReadDuration;
  }

  buildTemplateParams(personalInfo: any, merchantInfo: any): any {
    // 业务逻辑...
    return {
      contractPreviewData: { /* ... */ },
      contractCode: this.id,
      contractCategory: this.type,
      // ...
    };
  }

  isApplicableForMerchant(merchantId: string): boolean {
    const isSelfOperated = merchantId === '10000' || merchantId === '10001';
    const isStandardGroup = this.groupId === 'XYDC';
    return (isSelfOperated && isStandardGroup) || (!isSelfOperated && this.groupId === 'XYDCFZY');
  }

  // 访问器
  getId(): string { return this.id; }
  getType(): string { return this.type; }
  getGroupId(): string { return this.groupId; }
  getText(): string { return this.text; }
}

/**
 * 用户领域模型 - 单实例
 */
export class User {
  @observable private id: string;
  @observable private name: string;
  @observable private avatar: string;

  constructor(props: { id: string; name: string; avatar: string }) {
    this.id = props.id;
    this.name = props.name;
    this.avatar = props.avatar;
  }

  // 业务方法
  updateProfile(name: string, avatar: string): void {
    this.name = name;
    this.avatar = avatar;
  }

  // 访问器
  getId(): string { return this.id; }
  getName(): string { return this.name; }
  getAvatar(): string { return this.avatar; }
}

// ============================================================================
// 2. 仓储层 - 统一管理单实例和多实例
// ============================================================================

/**
 * 合同仓储 - 管理多实例集合
 */
export class ContractRepository {
  @observable private contracts: Map<string, Contract> = new Map();
  @observable private contractsByGroup: Map<string, Contract[]> = new Map();
  private cache: Map<string, any> = new Map();

  // ============================================================================
  // 实例管理方法
  // ============================================================================

  /**
   * 添加合同到仓储
   */
  @action
  private addContract(contract: Contract): void {
    this.contracts.set(contract.getId(), contract);
    
    // 更新分组索引
    const groupId = contract.getGroupId();
    if (!this.contractsByGroup.has(groupId)) {
      this.contractsByGroup.set(groupId, []);
    }
    const groupContracts = this.contractsByGroup.get(groupId)!;
    if (!groupContracts.find(c => c.getId() === contract.getId())) {
      groupContracts.push(contract);
    }
  }

  /**
   * 批量添加合同
   */
  @action
  private addContracts(contracts: Contract[]): void {
    contracts.forEach(contract => this.addContract(contract));
  }

  /**
   * 清空指定组的合同
   */
  @action
  private clearGroup(groupId: string): void {
    const groupContracts = this.contractsByGroup.get(groupId) || [];
    groupContracts.forEach(contract => {
      this.contracts.delete(contract.getId());
    });
    this.contractsByGroup.delete(groupId);
  }

  // ============================================================================
  // 数据访问方法
  // ============================================================================

  /**
   * 根据合同组ID获取合同列表
   */
  async findByGroupId(groupId: string): Promise<Contract[]> {
    try {
      // 1. 检查内存中是否已有数据
      if (this.contractsByGroup.has(groupId)) {
        return this.contractsByGroup.get(groupId)!;
      }

      // 2. 检查缓存
      const cacheKey = `contracts:${groupId}`;
      let apiData;
      if (this.cache.has(cacheKey)) {
        apiData = this.cache.get(cacheKey);
      } else {
        // 3. 调用API获取数据
        const contractConfig = await queryContractConfig(groupId);
        if (!contractConfig || !contractConfig.contracts) {
          return [];
        }
        apiData = contractConfig.contracts;
        this.cache.set(cacheKey, apiData);
      }

      // 4. 转换为领域对象并添加到仓储
      const contracts = apiData.map((item: any) => new Contract({
        id: item.contractId || item.contractCode || item.id || '',
        type: item.contractType || '',
        groupId: groupId,
        text: item.contractText || item.text || '',
        forceReadFlag: item.forceReadFlag || 'N',
        forceReadDuration: parseInt(item.forceReadDuration || item.readDuration) || 0,
        isOldContract: !item.contractCode
      }));

      this.addContracts(contracts);
      return contracts;

    } catch (error) {
      console.error('获取合同配置失败:', error);
      return [];
    }
  }

  /**
   * 获取单个合同
   */
  getContract(id: string): Contract | undefined {
    return this.contracts.get(id);
  }

  /**
   * 获取所有合同
   */
  @computed
  get allContracts(): Contract[] {
    return Array.from(this.contracts.values());
  }

  /**
   * 获取强制阅读合同
   */
  getForceReadContracts(groupId: string): Contract[] {
    const groupContracts = this.contractsByGroup.get(groupId) || [];
    return groupContracts.filter(contract => contract.isForceReadRequired);
  }

  /**
   * 获取适用于指定商户的合同
   */
  getApplicableContracts(groupId: string, merchantId: string): Contract[] {
    const groupContracts = this.contractsByGroup.get(groupId) || [];
    return groupContracts.filter(contract => contract.isApplicableForMerchant(merchantId));
  }

  /**
   * 获取合同模板
   */
  async getContractTemplate(contract: Contract, personalInfo: any, merchantInfo: any): Promise<any> {
    try {
      const params = contract.buildTemplateParams(personalInfo, merchantInfo);
      const htmlFile = await queryContractTemplate(params);
      
      return {
        title: contract.getText(),
        htmlFile: htmlFile,
        contractId: contract.getId(),
        isForceRead: contract.isForceReadRequired,
        readDuration: contract.readDuration
      };
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw new Error('获取合同模板失败');
    }
  }

  /**
   * 批量获取合同模板
   */
  async getContractTemplates(groupId: string, personalInfo: any, merchantInfo: any): Promise<any[]> {
    const contracts = await this.findByGroupId(groupId);
    const applicableContracts = contracts.filter(contract => 
      contract.isApplicableForMerchant(merchantInfo.merchantId)
    );

    const promises = applicableContracts.map(contract => 
      this.getContractTemplate(contract, personalInfo, merchantInfo)
    );
    
    return await Promise.all(promises);
  }

  /**
   * 刷新指定组的合同数据
   */
  async refreshGroup(groupId: string): Promise<Contract[]> {
    // 清空内存和缓存
    this.clearGroup(groupId);
    this.cache.delete(`contracts:${groupId}`);
    
    // 重新获取
    return await this.findByGroupId(groupId);
  }

  /**
   * 清空所有数据
   */
  @action
  clear(): void {
    this.contracts.clear();
    this.contractsByGroup.clear();
    this.cache.clear();
  }
}

/**
 * 用户仓储 - 管理单实例
 */
export class UserRepository {
  @observable private userInstance: User | null = null;
  private cache: Map<string, any> = new Map();

  // ============================================================================
  // 单实例管理
  // ============================================================================

  /**
   * 获取当前用户实例
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      // 1. 如果内存中已有实例，直接返回
      if (this.userInstance) {
        return this.userInstance;
      }

      // 2. 检查缓存
      const cacheKey = 'current-user';
      let userData;
      if (this.cache.has(cacheKey)) {
        userData = this.cache.get(cacheKey);
      } else {
        // 3. 调用API获取数据
        userData = await queryCurrentUser();
        if (!userData) {
          return null;
        }
        this.cache.set(cacheKey, userData);
      }

      // 4. 创建用户实例
      this.userInstance = new User({
        id: userData.userId || '',
        name: userData.userName || '',
        avatar: userData.avatar || ''
      });

      return this.userInstance;

    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 更新用户信息
   */
  async updateUser(userData: { name?: string; avatar?: string }): Promise<User | null> {
    try {
      if (!this.userInstance) {
        throw new Error('用户实例不存在');
      }

      // 调用API更新
      const updatedData = await updateUserProfile(userData);
      
      // 更新本地实例
      if (userData.name) this.userInstance.updateProfile(userData.name, this.userInstance.getAvatar());
      if (userData.avatar) this.userInstance.updateProfile(this.userInstance.getName(), userData.avatar);
      
      // 更新缓存
      this.cache.set('current-user', updatedData);
      
      return this.userInstance;

    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw new Error('更新用户信息失败');
    }
  }

  /**
   * 清空用户实例
   */
  @action
  clearUser(): void {
    this.userInstance = null;
    this.cache.clear();
  }

  /**
   * 刷新用户数据
   */
  async refreshUser(): Promise<User | null> {
    this.clearUser();
    return await this.getCurrentUser();
  }
}

// ============================================================================
// 3. 仓储工厂 - 统一管理所有仓储实例
// ============================================================================

/**
 * 仓储工厂 - 单例模式管理所有仓储
 */
export class RepositoryFactory {
  private static contractRepository: ContractRepository | null = null;
  private static userRepository: UserRepository | null = null;

  /**
   * 获取合同仓储实例
   */
  static getContractRepository(): ContractRepository {
    if (!this.contractRepository) {
      this.contractRepository = new ContractRepository();
    }
    return this.contractRepository;
  }

  /**
   * 获取用户仓储实例
   */
  static getUserRepository(): UserRepository {
    if (!this.userRepository) {
      this.userRepository = new UserRepository();
    }
    return this.userRepository;
  }

  /**
   * 清空所有仓储
   */
  static clearAll(): void {
    if (this.contractRepository) {
      this.contractRepository.clear();
    }
    if (this.userRepository) {
      this.userRepository.clearUser();
    }
  }
}

// ============================================================================
// 4. 简化的服务层 - 主要负责编排
// ============================================================================

/**
 * 合同服务 - 简化为编排逻辑
 */
export class ContractService {
  private contractRepository: ContractRepository;

  constructor() {
    this.contractRepository = RepositoryFactory.getContractRepository();
  }

  /**
   * 初始化合同数据
   */
  async initializeContracts(contractGroupId: string): Promise<Contract[]> {
    return await this.contractRepository.findByGroupId(contractGroupId);
  }

  /**
   * 获取合同模板
   */
  async getContractTemplates(
    contractGroupId: string,
    personalInfo: any,
    merchantInfo: any
  ): Promise<any[]> {
    return await this.contractRepository.getContractTemplates(
      contractGroupId,
      personalInfo,
      merchantInfo
    );
  }

  /**
   * 获取强制阅读合同模板
   */
  async getForceReadTemplates(
    contractGroupId: string,
    personalInfo: any,
    merchantInfo: any
  ): Promise<any[]> {
    const forceReadContracts = this.contractRepository.getForceReadContracts(contractGroupId);
    const applicableContracts = forceReadContracts.filter(contract =>
      contract.isApplicableForMerchant(merchantInfo.merchantId)
    );

    const promises = applicableContracts.map(contract =>
      this.contractRepository.getContractTemplate(contract, personalInfo, merchantInfo)
    );

    return await Promise.all(promises);
  }

  /**
   * 获取合同仓储（供外部直接访问）
   */
  getRepository(): ContractRepository {
    return this.contractRepository;
  }
}

/**
 * 用户服务 - 简化为编排逻辑
 */
export class UserService {
  private userRepository: UserRepository;

  constructor() {
    this.userRepository = RepositoryFactory.getUserRepository();
  }

  /**
   * 获取当前用户
   */
  async getCurrentUser(): Promise<User | null> {
    return await this.userRepository.getCurrentUser();
  }

  /**
   * 更新用户信息
   */
  async updateUser(userData: { name?: string; avatar?: string }): Promise<User | null> {
    return await this.userRepository.updateUser(userData);
  }

  /**
   * 获取用户仓储（供外部直接访问）
   */
  getRepository(): UserRepository {
    return this.userRepository;
  }
}

// ============================================================================
// 5. 页面Store - 只管理UI状态，数据通过仓储访问
// ============================================================================

/**
 * 身份页面Store - 重构后只管理UI状态
 */
export class IdentityStore {
  // ============================================================================
  // UI状态管理
  // ============================================================================

  @observable showContractsModal = false;
  @observable contractChecked = false;
  @observable hasCountDown = false;
  @observable onlyShowForceReadContracts = false;
  @observable isLoading = false;
  @observable error: string | null = null;
  @observable currentContractGroupId = '';

  // ============================================================================
  // 仓储引用
  // ============================================================================

  private contractRepository: ContractRepository;
  private userRepository: UserRepository;
  private contractService: ContractService;
  private userService: UserService;

  constructor() {
    this.contractRepository = RepositoryFactory.getContractRepository();
    this.userRepository = RepositoryFactory.getUserRepository();
    this.contractService = new ContractService();
    this.userService = new UserService();
  }

  // ============================================================================
  // 计算属性 - 从仓储获取数据
  // ============================================================================

  /**
   * 当前用户
   */
  @computed
  get currentUser(): User | null {
    return this.userRepository.userInstance;
  }

  /**
   * 当前组的所有合同
   */
  @computed
  get contracts(): Contract[] {
    if (!this.currentContractGroupId) return [];
    return this.contractRepository.contractsByGroup.get(this.currentContractGroupId) || [];
  }

  /**
   * 合同文本
   */
  @computed
  get contractText(): string {
    return this.contracts.map(c => c.getText()).join('、');
  }

  /**
   * 强制阅读合同
   */
  @computed
  get forceReadContracts(): Contract[] {
    if (!this.currentContractGroupId) return [];
    return this.contractRepository.getForceReadContracts(this.currentContractGroupId);
  }

  /**
   * 是否有强制阅读合同
   */
  @computed
  get hasForceReadContracts(): boolean {
    return this.forceReadContracts.length > 0;
  }

  /**
   * 强制阅读时长
   */
  @computed
  get forceReadDuration(): number {
    const forceReadContracts = this.forceReadContracts;
    return forceReadContracts.length > 0 ? forceReadContracts[0].readDuration : 0;
  }

  // ============================================================================
  // 业务方法
  // ============================================================================

  /**
   * 初始化页面数据
   */
  @action
  async initializePage(contractGroupId: string): Promise<void> {
    try {
      this.isLoading = true;
      this.error = null;
      this.currentContractGroupId = contractGroupId;

      // 并行初始化用户和合同数据
      await Promise.all([
        this.userService.getCurrentUser(),
        this.contractService.initializeContracts(contractGroupId)
      ]);

    } catch (error) {
      this.error = error.message || '初始化页面数据失败';
      console.error('初始化页面数据失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理合同点击
   */
  @action
  handleContractClick = (isClickCheckbox = false) => {
    if (isClickCheckbox && this.hasForceReadContracts && !this.hasCountDown) {
      this.showContractModal(true);
    } else {
      this.showContractModal(false);
    }
  }

  @action
  showContractModal = (onlyShowForceRead = false) => {
    this.onlyShowForceReadContracts = onlyShowForceRead;
    this.showContractsModal = true;
  }

  @action
  hideContractModal = () => {
    this.showContractsModal = false;
  }

  @action
  setContractChecked = (checked: boolean) => {
    this.contractChecked = checked;
  }

  @action
  setCountDownComplete = () => {
    this.hasCountDown = true;
  }

  /**
   * 刷新合同数据
   */
  @action
  async refreshContracts(): Promise<void> {
    if (!this.currentContractGroupId) return;

    try {
      this.isLoading = true;
      await this.contractRepository.refreshGroup(this.currentContractGroupId);
    } catch (error) {
      this.error = error.message || '刷新合同数据失败';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 获取合同仓储（供组件直接访问）
   */
  getContractRepository(): ContractRepository {
    return this.contractRepository;
  }

  /**
   * 获取用户仓储（供组件直接访问）
   */
  getUserRepository(): UserRepository {
    return this.userRepository;
  }
}
