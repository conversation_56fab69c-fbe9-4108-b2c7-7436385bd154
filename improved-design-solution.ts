// ============================================================================
// 改进的DDD设计方案 - 解决关键问题
// ============================================================================

// 问题1：纯领域模型如何与视图层数据绑定？
// 问题2：业务逻辑如何沉淀到model层？
// 问题3：单实例vs多实例的集合维护？

// ============================================================================
// 解决方案1：保留MobX响应式，但只用于业务属性
// ============================================================================

import { observable, action, computed } from 'mobx';

// ============================================================================
// 解决方案2：简化仓储层 - 直接调用API
// ============================================================================

import { queryContractTemplate, queryContractConfig } from '../../api/contract/api';

// ============================================================================
// 页面Store重构 - 解决数据绑定问题
// ============================================================================

import { observable, action, computed } from 'mobx';

/**
 * 合同领域模型 - 保留响应式，移除UI状态
 *
 * 解决方案：
 * 1. 保留@observable用于数据绑定，但只用于业务属性
 * 2. 移除UI状态（如isShowContract、contractParams等）
 * 3. 业务逻辑沉淀到model层
 */
export class Contract {
  @observable private id: string;
  @observable private type: string;
  @observable private groupId: string;
  @observable private text: string;
  @observable private version: string;
  @observable private forceReadFlag: string;
  @observable private forceReadDuration: number;
  @observable private isOldContract: boolean;
  @observable private infoList: any[] | null;

  constructor(props: {
    id: string;
    type: string;
    groupId: string;
    text: string;
    version?: string;
    forceReadFlag: string;
    forceReadDuration: number;
    isOldContract: boolean;
    infoList?: any[] | null;
  }) {
    this.id = props.id;
    this.type = props.type;
    this.groupId = props.groupId;
    this.text = props.text;
    this.version = props.version || '1.0';
    this.forceReadFlag = props.forceReadFlag;
    this.forceReadDuration = props.forceReadDuration;
    this.isOldContract = props.isOldContract;
    this.infoList = props.infoList || null;
  }

  // ============================================================================
  // 业务逻辑沉淀到model层
  // ============================================================================

  /**
   * 是否需要强制阅读 - 使用computed保持响应式
   */
  @computed
  get isForceReadRequired(): boolean {
    return this.forceReadFlag === 'Y';
  }

  /**
   * 获取强制阅读时长
   */
  @computed
  get readDuration(): number {
    return this.forceReadDuration;
  }

  /**
   * 是否为旧版合同
   */
  @computed
  get isLegacy(): boolean {
    return this.isOldContract;
  }

  /**
   * 构建合同模板参数 - 核心业务逻辑
   */
  buildTemplateParams(personalInfo: any, merchantInfo: any): any {
    const baseContractInfo = {
      name: personalInfo.custName || '',
      certId: personalInfo.certId || '',
      dateNow: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      mobile: personalInfo.custMobile || '',
      signDate: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
      partnerInfoList: [{
        partnerId: merchantInfo.partnerId || merchantInfo.merchantId,
        partner: merchantInfo.partner || merchantInfo.orgName || merchantInfo.merchantName,
        partnerType: merchantInfo.partnerType || '01',
        partnerContact: merchantInfo.custservicePhoneNo,
      }],
    };

    return {
      contractPreviewData: {
        baseContractInfo,
        rechargeInfo: { phoneNo: merchantInfo.merchantMobile },
      },
      contractVersion: this.isOldContract ? '' : this.version,
      contractCode: this.id,
      contractCategory: this.type,
      interfaceVersion: this.isOldContract ? '2.0' : '3.0',
      scene: 'PREVIEW',
    };
  }

  /**
   * 验证合同是否适用于指定商户 - 业务规则
   */
  isApplicableForMerchant(merchantId: string): boolean {
    const isSelfOperated = merchantId === '10000' || merchantId === '10001';
    const isStandardGroup = this.groupId === 'XYDC';
    const isNonSelfOperatedGroup = this.groupId === 'XYDCFZY';

    return (isSelfOperated && isStandardGroup) || (!isSelfOperated && isNonSelfOperatedGroup);
  }

  /**
   * 生成合同文本摘要 - 业务逻辑
   */
  @computed
  get textSummary(): string {
    if (this.text.length <= 20) return this.text;
    return `${this.text.substring(0, 20)}...`;
  }

  // ============================================================================
  // 数据更新方法
  // ============================================================================

  @action
  updateInfo(data: Partial<{
    text: string;
    version: string;
    forceReadFlag: string;
    forceReadDuration: number;
    infoList: any[];
  }>): void {
    if (data.text !== undefined) this.text = data.text;
    if (data.version !== undefined) this.version = data.version;
    if (data.forceReadFlag !== undefined) this.forceReadFlag = data.forceReadFlag;
    if (data.forceReadDuration !== undefined) this.forceReadDuration = data.forceReadDuration;
    if (data.infoList !== undefined) this.infoList = data.infoList;
  }

  // 访问器
  getId(): string { return this.id; }
  getType(): string { return this.type; }
  getGroupId(): string { return this.groupId; }
  getText(): string { return this.text; }
}

// ============================================================================
// 解决方案3：合同集合管理 - 多实例维护
// ============================================================================

/**
 * 合同集合管理器 - 管理多个合同实例
 *
 * 职责：
 * 1. 维护合同集合
 * 2. 提供集合级别的业务方法
 * 3. 管理合同的生命周期
 */
export class ContractCollection {
  @observable private contracts: Map<string, Contract> = new Map();

  /**
   * 添加合同
   */
  @action
  addContract(contract: Contract): void {
    this.contracts.set(contract.getId(), contract);
  }

  /**
   * 批量添加合同
   */
  @action
  addContracts(contracts: Contract[]): void {
    contracts.forEach(contract => {
      this.contracts.set(contract.getId(), contract);
    });
  }

  /**
   * 获取合同
   */
  getContract(id: string): Contract | undefined {
    return this.contracts.get(id);
  }

  /**
   * 获取所有合同
   */
  @computed
  get allContracts(): Contract[] {
    return Array.from(this.contracts.values());
  }

  /**
   * 获取强制阅读合同 - 集合级别的业务逻辑
   */
  @computed
  get forceReadContracts(): Contract[] {
    return this.allContracts.filter(contract => contract.isForceReadRequired);
  }

  /**
   * 获取指定组的合同
   */
  getContractsByGroup(groupId: string): Contract[] {
    return this.allContracts.filter(contract => contract.getGroupId() === groupId);
  }

  /**
   * 获取适用于指定商户的合同
   */
  getApplicableContracts(merchantId: string): Contract[] {
    return this.allContracts.filter(contract => contract.isApplicableForMerchant(merchantId));
  }

  /**
   * 清空集合
   */
  @action
  clear(): void {
    this.contracts.clear();
  }

  /**
   * 移除合同
   */
  @action
  removeContract(id: string): void {
    this.contracts.delete(id);
  }

  /**
   * 合同数量
   */
  @computed
  get count(): number {
    return this.contracts.size;
  }

  /**
   * 是否为空
   */
  @computed
  get isEmpty(): boolean {
    return this.contracts.size === 0;
  }
}

/**
 * 合同仓储 - 简化版，直接调用API
 *
 * 职责：
 * 1. 数据获取和转换
 * 2. 缓存管理
 * 3. 错误处理
 */
export class ContractRepository {
  private cache: Map<string, any> = new Map();

  /**
   * 根据合同组ID获取合同配置
   */
  async findByGroupId(groupId: string): Promise<Contract[]> {
    try {
      // 检查缓存
      const cacheKey = `contracts:${groupId}`;
      if (this.cache.has(cacheKey)) {
        const cachedData = this.cache.get(cacheKey);
        return cachedData.map((item: any) => this.createContractFromApiData(item));
      }

      // 直接调用API
      const contractConfig = await queryContractConfig(groupId);

      if (!contractConfig || !contractConfig.contracts) {
        return [];
      }

      // 缓存原始数据
      this.cache.set(cacheKey, contractConfig.contracts);

      // 转换为领域对象
      return contractConfig.contracts.map((item: any) => this.createContractFromApiData(item));
    } catch (error) {
      console.error('获取合同配置失败:', error);
      return [];
    }
  }

  /**
   * 获取合同模板
   */
  async getContractTemplate(contract: Contract, personalInfo: any, merchantInfo: any): Promise<any> {
    try {
      // 使用合同的业务方法构建参数
      const params = contract.buildTemplateParams(personalInfo, merchantInfo);

      // 直接调用API
      const htmlFile = await queryContractTemplate(params);

      return {
        title: contract.getText(),
        htmlFile: htmlFile,
        contractId: contract.getId(),
        isForceRead: contract.isForceReadRequired,
        readDuration: contract.readDuration,
      };
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw new Error('获取合同模板失败');
    }
  }

  /**
   * 批量获取合同模板
   */
  async getContractTemplates(contracts: Contract[], personalInfo: any, merchantInfo: any): Promise<any[]> {
    const promises = contracts.map(contract =>
      this.getContractTemplate(contract, personalInfo, merchantInfo),
    );

    return await Promise.all(promises);
  }

  /**
   * 从API数据创建合同对象
   */
  private createContractFromApiData(apiData: any): Contract {
    return new Contract({
      id: apiData.contractId || apiData.contractCode || apiData.id || '',
      type: apiData.contractType || '',
      groupId: apiData.contractGroupId || '',
      text: apiData.contractText || apiData.text || '',
      version: apiData.contractVersion || '1.0',
      forceReadFlag: apiData.forceReadFlag || 'N',
      forceReadDuration: parseInt(apiData.forceReadDuration || apiData.readDuration) || 0,
      isOldContract: !apiData.contractCode,
      infoList: apiData.contractInfoList || null,
    });
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// ============================================================================
// 服务层简化 - 哪些service需要保留？
// ============================================================================

/**
 * 分析现有服务：
 *
 * 1. contract-service.ts - 可以简化
 *    - 大部分业务逻辑移到Contract model
 *    - 只保留编排逻辑
 *
 * 2. apply-service.ts - 保留
 *    - 处理申请流程编排
 *    - 协调多个领域对象
 *
 * 3. form-validation-service.ts - 保留
 *    - 表单验证逻辑
 *    - 跨领域的验证规则
 *
 * 4. user-service.ts - 保留
 *    - 用户相关的业务逻辑
 */

/**
 * 简化后的合同服务 - 主要负责编排
 */
export class ContractService {
  private contractRepository: ContractRepository;
  private contractCollection: ContractCollection;

  constructor() {
    this.contractRepository = new ContractRepository();
    this.contractCollection = new ContractCollection();
  }

  /**
   * 初始化合同数据 - 编排逻辑
   */
  async initializeContracts(contractGroupId: string): Promise<ContractCollection> {
    try {
      // 1. 从仓储获取合同数据
      const contracts = await this.contractRepository.findByGroupId(contractGroupId);

      // 2. 添加到集合中
      this.contractCollection.clear();
      this.contractCollection.addContracts(contracts);

      return this.contractCollection;
    } catch (error) {
      console.error('初始化合同数据失败:', error);
      throw new Error('初始化合同数据失败');
    }
  }

  /**
   * 获取合同模板 - 编排逻辑
   */
  async getContractTemplates(
    contractGroupId: string,
    personalInfo: any,
    merchantInfo: any,
  ): Promise<any[]> {
    try {
      // 1. 确保合同数据已初始化
      if (this.contractCollection.isEmpty) {
        await this.initializeContracts(contractGroupId);
      }

      // 2. 获取适用的合同
      const applicableContracts = this.contractCollection.getApplicableContracts(
        merchantInfo.merchantId,
      );

      // 3. 生成模板
      return await this.contractRepository.getContractTemplates(
        applicableContracts,
        personalInfo,
        merchantInfo,
      );
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw new Error('获取合同模板失败');
    }
  }

  /**
   * 获取强制阅读合同模板
   */
  async getForceReadTemplates(
    contractGroupId: string,
    personalInfo: any,
    merchantInfo: any,
  ): Promise<any[]> {
    try {
      // 1. 确保合同数据已初始化
      if (this.contractCollection.isEmpty) {
        await this.initializeContracts(contractGroupId);
      }

      // 2. 获取强制阅读合同
      const forceReadContracts = this.contractCollection.forceReadContracts.filter(
        contract => contract.isApplicableForMerchant(merchantInfo.merchantId),
      );

      // 3. 生成模板
      return await this.contractRepository.getContractTemplates(
        forceReadContracts,
        personalInfo,
        merchantInfo,
      );
    } catch (error) {
      console.error('获取强制阅读合同模板失败:', error);
      throw new Error('获取强制阅读合同模板失败');
    }
  }

  /**
   * 获取合同集合 - 供外部访问
   */
  getContractCollection(): ContractCollection {
    return this.contractCollection;
  }
}

/**
 * 身份页面Store - 重构后的设计
 *
 * 解决方案：
 * 1. 使用ContractCollection管理合同数据
 * 2. UI状态独立管理
 * 3. 通过computed保持响应式
 */
class IdentityStore {
  // ============================================================================
  // 领域对象（保持响应式）
  // ============================================================================

  @observable formPersonalInfo = getFormPersonalInfo();
  @observable merchant = getMerchantInstance();
  @observable identityCard = getIdentityCardInstance();

  // 使用ContractCollection替代单个ContractModel
  @observable contractCollection: ContractCollection = new ContractCollection();

  // ============================================================================
  // UI状态管理
  // ============================================================================

  @observable showContractsModal = false;
  @observable contractChecked = false;
  @observable hasCountDown = false;
  @observable onlyShowForceReadContracts = false;
  @observable isLoading = false;
  @observable error: string | null = null;

  // ============================================================================
  // 服务实例
  // ============================================================================

  private contractService: ContractService;

  constructor() {
    this.contractService = new ContractService();
  }

  // ============================================================================
  // 计算属性 - 解决数据绑定问题
  // ============================================================================

  /**
   * 合同文本 - 从集合中计算
   */
  @computed
  get contractText(): string {
    const contracts = this.contractCollection.allContracts;
    return contracts.map(c => c.getText()).join('、');
  }

  /**
   * 强制阅读合同
   */
  @computed
  get forceReadContracts(): Contract[] {
    return this.contractCollection.forceReadContracts;
  }

  /**
   * 是否有强制阅读合同
   */
  @computed
  get hasForceReadContracts(): boolean {
    return this.forceReadContracts.length > 0;
  }

  /**
   * 强制阅读时长
   */
  @computed
  get forceReadDuration(): number {
    const { forceReadContracts } = this;
    return forceReadContracts.length > 0 ? forceReadContracts[0].readDuration : 0;
  }

  // ============================================================================
  // 业务方法
  // ============================================================================

  /**
   * 初始化合同数据
   */
  @action
  async initializeContracts(contractGroupId: string): Promise<void> {
    try {
      this.isLoading = true;
      this.error = null;

      // 使用服务初始化合同集合
      this.contractCollection = await this.contractService.initializeContracts(contractGroupId);
    } catch (error) {
      this.error = error.message || '初始化合同数据失败';
      console.error('初始化合同数据失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理合同点击
   */
  @action
  handleContractClick = (isClickCheckbox = false) => {
    const isFormValid = FormValidationService.checkFormData();
    if (!isFormValid) return;

    if (isClickCheckbox && this.hasForceReadContracts && !this.hasCountDown) {
      this.showContractModal(true);
    } else {
      this.showContractModal(false);
    }
  };

  @action
  showContractModal = (onlyShowForceRead = false) => {
    this.onlyShowForceReadContracts = onlyShowForceRead;
    this.showContractsModal = true;
  };

  @action
  hideContractModal = () => {
    this.showContractsModal = false;
  };
}
