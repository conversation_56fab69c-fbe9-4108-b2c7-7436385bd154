# 仓储模式（Repository Pattern）详解

## 核心概念

### 什么是仓储模式？

仓储模式是一种设计模式，它封装了数据访问逻辑，为领域层提供了一个更面向对象的方式来访问领域对象。仓储模拟了一个内存中的对象集合，隐藏了数据存储和查询的技术细节。

### 为什么要使用仓储模式？

1. **关注点分离**：将数据访问逻辑与业务逻辑分离
2. **测试友好**：便于创建Mock实现进行单元测试
3. **技术无关性**：领域层不依赖具体的数据访问技术
4. **可维护性**：数据访问逻辑集中管理，易于维护和修改

## 架构分层

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │        ContractApplicationService                       │ │
│  │  - getContractTemplates()                              │ │
│  │  - validateContractAgreement()                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ 依赖
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    领域层 (Domain)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              ContractRepository                         │ │
│  │  + findById(id): Contract                              │ │
│  │  + findByGroupId(groupId): Contract[]                  │ │
│  │  + findForceReadContracts(groupId): Contract[]         │ │
│  │  + save(contract): Contract                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ▲ 实现
                              │
┌─────────────────────────────────────────────────────────────┐
│                 基础设施层 (Infrastructure)                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           ContractRepositoryImpl                        │ │
│  │  - apiClient: ApiClient                                │ │
│  │  - cacheService: CacheService                          │ │
│  │  + findById(id): Contract                              │ │
│  │  + findByGroupId(groupId): Contract[]                  │ │
│  │  - toDomainEntity(apiData): Contract                   │ │
│  │  - toApiData(contract): any                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 关键设计原则

### 1. 依赖倒置原则 (DIP)

**错误的设计**：
```typescript
// ❌ 应用层直接依赖具体实现
class ContractApplicationService {
  constructor(
    private contractRepo: ContractRepositoryImpl  // 依赖具体实现
  ) {}
}
```

**正确的设计**：
```typescript
// ✅ 应用层依赖抽象接口
class ContractApplicationService {
  constructor(
    private contractRepo: ContractRepository  // 依赖抽象接口
  ) {}
}
```

### 2. 接口隔离原则 (ISP)

```typescript
// ✅ 根据不同的职责定义不同的接口
interface ContractRepository extends BaseRepository<Contract, ContractId> {
  findByGroupId(groupId: string): Promise<Contract[]>;
  findForceReadContracts(groupId: string): Promise<Contract[]>;
}

interface ContractTemplateRepository extends BaseRepository<ContractTemplate, string> {
  findLatestTemplate(contractId: ContractId): Promise<ContractTemplate | null>;
  generateTemplate(params: ContractTemplateParams): Promise<string>;
}
```

### 3. 单一职责原则 (SRP)

每个仓储只负责一个聚合根的数据访问：

```typescript
// ✅ 每个仓储专注于一个聚合根
class ContractRepositoryImpl implements ContractRepository {
  // 只处理 Contract 聚合根相关的数据访问
}

class PersonalInfoRepositoryImpl implements PersonalInfoRepository {
  // 只处理 PersonalInfo 聚合根相关的数据访问
}
```

## 实现要点

### 1. 数据转换

仓储实现需要处理外部数据格式与领域对象之间的转换：

```typescript
class ContractRepositoryImpl {
  // 外部数据 → 领域对象
  private toDomainEntity(apiData: any): Contract | null {
    try {
      const contractId = new ContractId(apiData.contractId);
      const contractType = new ContractType(apiData.contractType);
      
      return Contract.create({
        id: contractId,
        type: contractType,
        // ... 其他属性
      });
    } catch (error) {
      return null; // 转换失败返回null
    }
  }

  // 领域对象 → 外部数据
  private toApiData(contract: Contract): any {
    return {
      contractId: contract.getId().getValue(),
      contractType: contract.getType().getValue(),
      // ... 其他字段映射
    };
  }
}
```

### 2. 错误处理

仓储应该优雅地处理各种错误情况：

```typescript
async findById(id: ContractId): Promise<Contract | null> {
  try {
    const response = await this.apiClient.get(`/contracts/${id.getValue()}`);
    return this.toDomainEntity(response.data);
  } catch (error) {
    // 记录错误但不抛出异常
    console.error('Failed to find contract:', error);
    return null; // 返回null表示未找到
  }
}
```

### 3. 缓存策略

在仓储实现中可以加入缓存逻辑：

```typescript
async findById(id: ContractId): Promise<Contract | null> {
  // 1. 先查缓存
  const cacheKey = `contract:${id.getValue()}`;
  const cached = await this.cacheService.get(cacheKey);
  if (cached) {
    return this.toDomainEntity(cached);
  }

  // 2. 查数据源
  const contract = await this.fetchFromDataSource(id);
  
  // 3. 缓存结果
  if (contract) {
    await this.cacheService.set(cacheKey, contract, 300);
  }
  
  return contract;
}
```

## 测试策略

### 1. Mock仓储

```typescript
class MockContractRepository implements ContractRepository {
  private contracts = new Map<string, Contract>();

  async findById(id: ContractId): Promise<Contract | null> {
    return this.contracts.get(id.getValue()) || null;
  }

  // 测试辅助方法
  addTestData(contract: Contract): void {
    this.contracts.set(contract.getId().getValue(), contract);
  }
}
```

### 2. 单元测试

```typescript
describe('ContractApplicationService', () => {
  let service: ContractApplicationService;
  let mockRepo: MockContractRepository;

  beforeEach(() => {
    mockRepo = new MockContractRepository();
    service = new ContractApplicationService(mockRepo);
  });

  it('should return contracts for valid group', async () => {
    // 准备测试数据
    const testContract = createTestContract();
    mockRepo.addTestData(testContract);

    // 执行测试
    const result = await service.getContractTemplates({ groupId: 'XYDC' });

    // 验证结果
    expect(result).toHaveLength(1);
  });
});
```

## 常见问题

### Q: 为什么不直接在应用层调用API？

**A**: 直接调用API会导致：
- 应用层与具体技术耦合
- 难以进行单元测试
- 数据转换逻辑分散
- 违反依赖倒置原则

### Q: 仓储和DAO有什么区别？

**A**: 
- **DAO**：面向数据，关注CRUD操作
- **Repository**：面向对象，关注业务查询，提供领域特有的查询方法

### Q: 一个聚合根对应一个仓储吗？

**A**: 是的，这是DDD的最佳实践：
- 每个聚合根有且仅有一个仓储
- 仓储负责整个聚合的持久化
- 不为聚合内的实体单独创建仓储

## 总结

仓储模式是DDD架构中的关键组件，它：

1. **分离关注点**：将数据访问与业务逻辑分离
2. **保持纯净**：让领域层保持技术无关性
3. **提高可测试性**：便于创建Mock进行单元测试
4. **增强灵活性**：可以轻松切换数据访问技术

通过正确实现仓储模式，我们可以构建出更加清晰、可维护、可测试的DDD架构。
