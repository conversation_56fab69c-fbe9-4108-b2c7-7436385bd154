# 前端 DDD 架构设计文档 (React + MobX)

## 1. 概述

本文档旨在为基于 React、MobX 和 TypeScript 的前端应用，提供一套可扩展、可维护的领域驱动设计（DDD）架构方案。该架构的核心目标是在保持业务逻辑清晰的同时，有效管理应用状态，特别是处理实体（Entity）的**单例（Singleton）**和**集合（Collection）**实例。

本方案省略了独立的**应用层（Application Layer）**，将其职责合并到**仓储层（Repository Layer）**，使其不仅负责数据持久化，还直接作为领域状态的响应式管理器。

### 核心技术栈

*   **React**: 用于构建用户界面。
*   **MobX**: 作为核心的状态管理库，将仓储层变为响应式的“活仓库”。
*   **TypeScript**: 提供类型安全，增强代码健壮性。

## 2. 核心设计原则

### 2.2. 仓储即状态 (Repository as State Manager)

传统的仓储主要负责数据的 CURD 抽象。在本架构中，仓储的职责被扩展：
*   **持有状态**: 仓储直接持有领域实体的实例。单例实体由一个属性持有 (`currentUser: User | null`)，集合实体由一个数组持有 (`todos: Todo[]`)。
*   **响应式核心**: 使用 MobX 的 `makeAutoObservable`，将仓储的内部属性变为 `observable`，方法变为 `action`，Getter 变为 `computed`。
*   **封装交互**: 仓储封装了所有与 API 的通信、数据传输对象 (DTO) 到领域实体的映射逻辑。

### 2.3. 保证单例：仓储工厂 (Repository Factory)

为了确保像 `UserRepository` 这样的服务在整个应用中是唯一的，我们引入**仓储工厂**的概念：
*   **单一实例化**: 创建一个中心化的 TypeScript 模块 (例如 `RepositoryFactory.ts`)。
*   **利用模块作用域**: 在该模块的顶层作用域 `new` 出所有仓储的实例并 `export`。
*   **全局唯一**: 根据 JavaScript (ES6) 模块规范，该模块只在应用加载时执行一次，因此导出的实例天然成为全局单例。

### 2.4. 依赖注入：按需提供 (Dependency Injection via Context)

UI 层不直接导入和使用仓储实例，而是通过页面 store 获取依赖：
*   **按需提供**: 每个页面的 store 从中央的**仓储工厂**中“挑选”自己需要的仓储实例，并提供给自己的组件树。

## 3. 数据流与交互

一个典型的用户交互流程如下：

1.  **用户操作**: 用户在 React 组件上触发一个事件（如点击按钮）。
2.  **调用仓储方法**: 组件获取到对应的仓储实例，并调用其上的一个 `action` 方法（如 `userRepo.updateName('new name')`）。
3.  **更新领域状态**: 仓储的 `action` 方法内部：
    a. 调用领域实体自身的业务方法 (`user.updateName(...)`)。
    b. 乐观更新 `observable` 状态。
    c. （异步）调用 API 进行数据持久化。
4.  **MobX 自动响应**: MobX 侦测到 `observable` 状态的变化。
5.  **UI 自动更新**: 所有包裹在 `observer` 中且使用了该状态的 React 组件都会自动重新渲染，展示最新的数据。



## 4. 项目代码示例

### 4.1. 项目结构

```bash

/src
├── api
│   ├── apply
│   │   ├── api.ts
│   │   ├── mock.ts
│   │   └── translator.ts
│   ├── contract
│   │   ├── api.ts
│   │   └── translator.ts
│   └── user
│       ├── index.ts
│       └── translator.ts
├── app.scss
├── app.tsx
├── assets
│   └── img
│       └── selfie_3x.png
├── components
│   └── demo
│       └── index.tsx
├── constants
│   ├── index-new.ts
│   ├── index.ts
│   ├── mock-config.ts
│   ├── setup-config.ts
│   ├── setupConfig.ts
│   └── theme.ts
├── domain  # 领域层
│   ├── models
│   │   ├── apply
│   │   │   ├── apply.ts            # 聚合根
│   │   │   ├── identity-card.ts    # 实体
│   │   │   ├── merchant.vo.ts      # 值对象
│   │   │   └── personal-info.vo.ts
│   │   ├── contract
│   │   │   └── contract.ts
│   │   └── user
│   │       └── user.ts
│   ├── repositories                # 仓储
│   │   ├── apply.repository.ts
│   │   ├── contract.repository.ts
│   │   ├── user.repository.ts
│   │   └── repository-factory.ts   # 核心：仓储工厂，创建并导出所有单例
│   └── services
│       ├── apply-service.ts
│       ├── contract-service.ts
│       ├── form-validation-service.ts
│       └── user-service.ts
├── index.html
├── pages                       # 表现层
│   ├── external-libs-demo
│   ├── homepage
│   │   ├── components
│   │   │   └── personal-setting
│   │   │       ├── index.scss
│   │   │       └── index.tsx
│   │   ├── index.tsx
│   │   └── store.ts                # UI store
│   ├── identity
│   │   ├── README.md
│   │   ├── components
│   │   │   ├── contract-checker
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   ├── identity-choice
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   ├── popup-picker
│   │   │   │   ├── index.scss
│   │   │   │   └── index.tsx
│   │   │   └── school-info
│   │   │       ├── index.scss
│   │   │       └── index.tsx
│   │   ├── index.scss
│   │   ├── index.tsx
│   │   └── store.ts
│   └── index
│       ├── index.tsx
│       └── store.ts
├── styles
│   ├── mixins.scss
│   ├── reset.scss
│   └── themes
│       ├── default.scss
│       ├── purple.scss
│       └── red.scss
├── types
│   ├── api.d.ts
│   ├── apply.d.ts
│   ├── contract.d.ts
│   └── user.d.ts
└── utils
    ├── contract-params.ts
    ├── data-pool.ts
    ├── h5-login.ts
    └── index.ts

### 4.2. 代码实现

#### **第 1 步: 领域层 (Domain)**

实体保持纯净，不含任何框架代码。

**`src/domain/user/user.ts`**
```typescript
export class User {
  constructor(
    public readonly id: number,
    public name: string,
    public email: string
  ) {}

  public updateName(newName: string): void {
    if (newName && newName.length > 2) {
      this.name = newName;
    }
  }
}
```

**`src/domain/todo/todo.ts`**
```typescript
export class Todo {
  constructor(
    public readonly id: number,
    public title: string,
    public completed: boolean
  ) {}

  public toggleCompletion(): void {
    this.completed = !this.completed;
  }
}
```

---

#### **第 2 步: 基础设施层 (Infrastructure)**

**`src/domain/repositories/repository-factory.ts` (核心)**
```typescript
import { UserRepository } from "./repository/UserRepository";
import { TodoRepository } from "./repository/TodoRepository";

/**
 * RepositoryFactory (仓储工厂)
 *
 * 这是整个应用的“服务容器”或“注册表”。
 * 这里的代码在应用加载时只会执行一次，因此每个 Repository 都是一个单例。
 * 所有需要 Repository 的地方都应该从这里导入，而不是自己 new 一个。
 */
export const userRepository = new UserRepository();
export const todoRepository = new TodoRepository();
```

**`src/domain/repositories/UserRepository.ts`**
```typescript
import { makeAutoObservable, runInAction } from "mobx";
import { User } from "../../domain/user/user";
import { mockApi } from "../api/mockApi";

export class UserRepository {
  // 单例实体实例的维护点
  public currentUser: User | null = null;
  public isLoading: boolean = false;

  constructor() {
    makeAutoObservable(this);
  }

  public async fetchCurrentUser(): Promise<void> {
    if (this.currentUser) return;
    this.isLoading = true;
    try {
      const userDTO = await mockApi.fetchCurrentUser();
      runInAction(() => {
        this.currentUser = new User(userDTO.id, userDTO.name, userDTO.email);
        this.isLoading = false;
      });
    } catch (error) {
      runInAction(() => { this.isLoading = false; });
    }
  }

  public async updateUserName(newName: string): Promise<void> {
    if (!this.currentUser) return;
    // 1. 调用实体业务逻辑
    this.currentUser.updateName(newName);
    // 2. 持久化 (这里可以加 try/catch 处理失败回滚)
    await mockApi.saveUserName(this.currentUser.id, this.currentUser.name);
  }
}
```

**`src/domain/repositories/TodoRepository.ts`**
```typescript
import { makeAutoObservable, runInAction } from "mobx";
import { Todo } from "../../domain/todo/Todo.entity";
import { mockApi } from "../api/mockApi";

export class TodoRepository {
  // 集合实体实例的维护点
  public todos: Todo[] = [];
  public isLoading: boolean = false;

  constructor() {
    makeAutoObservable(this);
  }

  // 计算属性
  public get unfinishedTodoCount(): number {
    return this.todos.filter(todo => !todo.completed).length;
  }

  public async fetchAllTodos(): Promise<void> {
    this.isLoading = true;
    try {
      const todoDTOs = await mockApi.fetchAllTodos();
      runInAction(() => {
        this.todos = todoDTOs.map(dto => new Todo(dto.id, dto.title, dto.completed));
        this.isLoading = false;
      });
    } catch (error) {
        runInAction(() => { this.isLoading = false; });
    }
  }

  public toggleTodoCompletion(todoId: number): void {
    const todo = this.todos.find(t => t.id === todoId);
    if (todo) {
      // 调用实体业务逻辑，MobX 会自动侦测到变化
      todo.toggleCompletion();
    }
  }
}
```
