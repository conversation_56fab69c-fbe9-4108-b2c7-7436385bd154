// ============================================================================
// 仓储模式设计详解 - DDD 架构中的数据访问层设计
// ============================================================================

// ============================================================================
// 1. 领域层 - 仓储接口定义 (domain/repositories/)
// ============================================================================

/**
 * 基础仓储接口 - 定义通用的 CRUD 操作
 * 
 * 这是所有仓储接口的基类，定义了最基本的数据访问操作。
 * 使用泛型来支持不同的实体类型。
 */
export interface BaseRepository<T, ID> {
  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 实体对象或null
   */
  findById(id: ID): Promise<T | null>;

  /**
   * 查找所有实体
   * @returns 实体列表
   */
  findAll(): Promise<T[]>;

  /**
   * 保存实体（新增或更新）
   * @param entity 实体对象
   * @returns 保存后的实体
   */
  save(entity: T): Promise<T>;

  /**
   * 删除实体
   * @param id 实体ID
   */
  delete(id: ID): Promise<void>;

  /**
   * 检查实体是否存在
   * @param id 实体ID
   * @returns 是否存在
   */
  exists(id: ID): Promise<boolean>;
}

/**
 * 合同仓储接口 - 定义合同特有的查询方法
 * 
 * 继承基础仓储接口，并添加合同领域特有的查询方法。
 * 这些方法体现了合同领域的业务查询需求。
 */
export interface ContractRepository extends BaseRepository<Contract, ContractId> {
  /**
   * 根据合同组ID查找合同列表
   * @param groupId 合同组ID
   * @returns 合同列表
   */
  findByGroupId(groupId: string): Promise<Contract[]>;

  /**
   * 根据合同类型查找合同
   * @param contractType 合同类型
   * @returns 合同列表
   */
  findByType(contractType: ContractType): Promise<Contract[]>;

  /**
   * 查找需要强制阅读的合同
   * @param groupId 合同组ID
   * @returns 强制阅读的合同列表
   */
  findForceReadContracts(groupId: string): Promise<Contract[]>;

  /**
   * 根据商户信息查找适用的合同
   * @param merchantId 商户ID
   * @param channelType 渠道类型
   * @returns 适用的合同列表
   */
  findByMerchantAndChannel(merchantId: string, channelType: string): Promise<Contract[]>;

  /**
   * 查找旧版本合同
   * @param groupId 合同组ID
   * @returns 旧版本合同列表
   */
  findLegacyContracts(groupId: string): Promise<Contract[]>;

  /**
   * 根据多个ID批量查找合同
   * @param ids 合同ID列表
   * @returns 合同列表
   */
  findByIds(ids: ContractId[]): Promise<Contract[]>;
}

/**
 * 合同模板仓储接口 - 管理合同模板数据
 */
export interface ContractTemplateRepository extends BaseRepository<ContractTemplate, string> {
  /**
   * 根据合同ID和版本查找模板
   * @param contractId 合同ID
   * @param version 版本号
   * @returns 合同模板
   */
  findByContractAndVersion(contractId: ContractId, version: string): Promise<ContractTemplate | null>;

  /**
   * 获取合同的最新模板
   * @param contractId 合同ID
   * @returns 最新的合同模板
   */
  findLatestTemplate(contractId: ContractId): Promise<ContractTemplate | null>;

  /**
   * 根据合同参数生成模板内容
   * @param templateParams 模板参数
   * @returns 生成的模板内容
   */
  generateTemplate(templateParams: ContractTemplateParams): Promise<string>;
}

// ============================================================================
// 2. 基础设施层 - 仓储实现 (infrastructure/repositories/)
// ============================================================================

/**
 * 合同仓储实现类 - 具体的数据访问实现
 * 
 * 实现了 ContractRepository 接口，封装了具体的数据访问逻辑。
 * 这里可以使用任何数据访问技术：HTTP API、数据库、缓存等。
 */
export class ContractRepositoryImpl implements ContractRepository {
  constructor(
    private readonly apiClient: ApiClient,
    private readonly cacheService: CacheService,
    private readonly configService: ConfigService
  ) {}

  async findById(id: ContractId): Promise<Contract | null> {
    try {
      // 1. 先从缓存查找
      const cacheKey = `contract:${id.getValue()}`;
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return this.toDomainEntity(cached);
      }

      // 2. 从API获取数据
      const response = await this.apiClient.get(`/contracts/${id.getValue()}`);
      if (!response.data) {
        return null;
      }

      // 3. 转换为领域实体
      const contract = this.toDomainEntity(response.data);

      // 4. 缓存结果
      await this.cacheService.set(cacheKey, response.data, 300); // 5分钟缓存

      return contract;
    } catch (error) {
      // 记录日志但不抛出异常，返回null表示未找到
      console.error('Failed to find contract by id:', error);
      return null;
    }
  }

  async findByGroupId(groupId: string): Promise<Contract[]> {
    try {
      // 1. 构建查询参数
      const params = {
        contractGroupId: groupId,
        status: 'ACTIVE'
      };

      // 2. 调用外部API
      const response = await this.apiClient.get('/contracts', { params });

      // 3. 数据转换和验证
      if (!response.data?.contracts) {
        return [];
      }

      // 4. 转换为领域实体列表
      return response.data.contracts
        .map(item => this.toDomainEntity(item))
        .filter(contract => contract !== null); // 过滤掉转换失败的数据

    } catch (error) {
      console.error('Failed to find contracts by group id:', error);
      return [];
    }
  }

  async findForceReadContracts(groupId: string): Promise<Contract[]> {
    const allContracts = await this.findByGroupId(groupId);
    
    // 使用领域对象的业务方法进行过滤
    return allContracts.filter(contract => contract.isForceReadRequired());
  }

  async findByMerchantAndChannel(merchantId: string, channelType: string): Promise<Contract[]> {
    try {
      // 1. 根据商户和渠道信息确定合同组
      const contractGroupId = await this.determineContractGroup(merchantId, channelType);

      // 2. 获取该组下的所有合同
      return await this.findByGroupId(contractGroupId);

    } catch (error) {
      console.error('Failed to find contracts by merchant and channel:', error);
      return [];
    }
  }

  async save(contract: Contract): Promise<Contract> {
    try {
      // 1. 转换为API数据格式
      const apiData = this.toApiData(contract);

      // 2. 调用保存API
      const response = await this.apiClient.post('/contracts', apiData);

      // 3. 清除相关缓存
      await this.clearRelatedCache(contract);

      // 4. 返回更新后的实体
      return this.toDomainEntity(response.data);

    } catch (error) {
      console.error('Failed to save contract:', error);
      throw new Error('合同保存失败');
    }
  }

  // ... 其他方法实现

  /**
   * 将API数据转换为领域实体
   * 这是适配器模式的应用，隔离外部数据格式变化对领域层的影响
   */
  private toDomainEntity(apiData: any): Contract | null {
    try {
      // 数据验证
      if (!apiData || !apiData.contractId) {
        return null;
      }

      // 创建值对象
      const contractId = new ContractId(apiData.contractId);
      const contractType = new ContractType(apiData.contractType);
      const forceReadConfig = new ForceReadConfig(
        apiData.forceReadFlag,
        parseInt(apiData.forceReadDuration) || 0
      );

      // 使用工厂方法创建实体
      return Contract.create({
        id: contractId,
        type: contractType,
        groupId: apiData.contractGroupId,
        text: apiData.contractText,
        version: apiData.contractVersion,
        forceReadConfig: forceReadConfig,
        isOldContract: !apiData.contractCode
      });

    } catch (error) {
      console.error('Failed to convert API data to domain entity:', error);
      return null;
    }
  }

  /**
   * 将领域实体转换为API数据格式
   */
  private toApiData(contract: Contract): any {
    return {
      contractId: contract.getId().getValue(),
      contractType: contract.getType().getValue(),
      contractGroupId: contract.getGroupId(),
      contractText: contract.getText(),
      contractVersion: contract.getVersion(),
      forceReadFlag: contract.getForceReadConfig().isRequired() ? 'Y' : 'N',
      forceReadDuration: contract.getForceReadConfig().getDuration().toString(),
      // ... 其他字段映射
    };
  }

  /**
   * 根据商户和渠道确定合同组
   * 这里封装了业务规则：不同商户和渠道使用不同的合同组
   */
  private async determineContractGroup(merchantId: string, channelType: string): Promise<string> {
    // 自营渠道使用标准协议
    if (merchantId === '10000' || merchantId === '10001') {
      return 'XYDC';
    }
    
    // 非自营渠道使用非自营协议
    return 'XYDCFZY';
  }

  /**
   * 清除相关缓存
   */
  private async clearRelatedCache(contract: Contract): Promise<void> {
    const patterns = [
      `contract:${contract.getId().getValue()}`,
      `contracts:group:${contract.getGroupId()}`,
      'contracts:force-read:*'
    ];

    await Promise.all(
      patterns.map(pattern => this.cacheService.deletePattern(pattern))
    );
  }
}

// ============================================================================
// 3. 为什么要分离接口和实现？
// ============================================================================

/**
 * 分离的核心原因：
 *
 * 1. 依赖倒置原则（DIP）
 *    - 高层模块（领域层/应用层）不应该依赖低层模块（基础设施层）
 *    - 两者都应该依赖抽象（接口）
 *
 * 2. 测试友好性
 *    - 可以轻松创建Mock实现进行单元测试
 *    - 不需要真实的数据库或API连接
 *
 * 3. 技术无关性
 *    - 领域层不关心数据来源（数据库、API、文件等）
 *    - 可以随时切换数据访问技术而不影响业务逻辑
 *
 * 4. 可扩展性
 *    - 可以有多个实现（如：缓存实现、数据库实现、API实现）
 *    - 可以使用装饰器模式组合多种实现
 */

// ============================================================================
// 4. 应用层中的使用示例
// ============================================================================

/**
 * 合同应用服务 - 展示如何在应用层使用仓储
 */
export class ContractApplicationService {
  constructor(
    // 依赖注入仓储接口，而不是具体实现
    private readonly contractRepository: ContractRepository,
    private readonly contractTemplateRepository: ContractTemplateRepository,
    private readonly personalInfoRepository: PersonalInfoRepository
  ) {}

  /**
   * 获取合同模板 - 应用层用例
   */
  async getContractTemplates(request: GetContractTemplatesRequest): Promise<ContractTemplateDto[]> {
    // 1. 参数验证
    if (!request.contractGroupId) {
      throw new Error('合同组ID不能为空');
    }

    // 2. 通过仓储获取领域对象
    const contracts = await this.contractRepository.findByGroupId(request.contractGroupId);

    if (contracts.length === 0) {
      return [];
    }

    // 3. 获取个人信息（用于模板参数）
    const personalInfo = await this.personalInfoRepository.getCurrent();

    // 4. 为每个合同生成模板
    const templatePromises = contracts.map(async contract => {
      // 获取最新模板
      const template = await this.contractTemplateRepository.findLatestTemplate(contract.getId());

      if (!template) {
        throw new Error(`合同模板不存在: ${contract.getId().getValue()}`);
      }

      // 构建模板参数
      const templateParams = this.buildTemplateParams(contract, personalInfo);

      // 生成模板内容
      const content = await this.contractTemplateRepository.generateTemplate(templateParams);

      return new ContractTemplateDto({
        contractId: contract.getId().getValue(),
        title: contract.getText(),
        content: content,
        isForceReadRequired: contract.isForceReadRequired(),
        readDuration: contract.getReadDuration()
      });
    });

    return await Promise.all(templatePromises);
  }

  private buildTemplateParams(contract: Contract, personalInfo: PersonalInfo): ContractTemplateParams {
    // 构建模板参数的业务逻辑
    return new ContractTemplateParams({
      contractId: contract.getId(),
      personalInfo: personalInfo,
      generateDate: new Date(),
      // ... 其他参数
    });
  }
}

// ============================================================================
// 5. 依赖注入配置示例
// ============================================================================

/**
 * IoC容器配置 - 将接口绑定到具体实现
 */
export class DIContainer {
  private container = new Container();

  configure(): void {
    // 绑定仓储接口到具体实现
    this.container.bind<ContractRepository>('ContractRepository')
      .to(ContractRepositoryImpl)
      .inSingletonScope();

    this.container.bind<ContractTemplateRepository>('ContractTemplateRepository')
      .to(ContractTemplateRepositoryImpl)
      .inSingletonScope();

    // 绑定应用服务
    this.container.bind<ContractApplicationService>('ContractApplicationService')
      .to(ContractApplicationService)
      .inSingletonScope();

    // 绑定基础设施服务
    this.container.bind<ApiClient>('ApiClient')
      .to(HttpApiClient)
      .inSingletonScope();

    this.container.bind<CacheService>('CacheService')
      .to(RedisCacheService)
      .inSingletonScope();
  }

  get<T>(identifier: string): T {
    return this.container.get<T>(identifier);
  }
}

// ============================================================================
// 6. 测试示例 - Mock仓储实现
// ============================================================================

/**
 * Mock合同仓储 - 用于单元测试
 */
export class MockContractRepository implements ContractRepository {
  private contracts: Map<string, Contract> = new Map();

  async findById(id: ContractId): Promise<Contract | null> {
    return this.contracts.get(id.getValue()) || null;
  }

  async findByGroupId(groupId: string): Promise<Contract[]> {
    return Array.from(this.contracts.values())
      .filter(contract => contract.getGroupId() === groupId);
  }

  async findForceReadContracts(groupId: string): Promise<Contract[]> {
    const contracts = await this.findByGroupId(groupId);
    return contracts.filter(contract => contract.isForceReadRequired());
  }

  async save(contract: Contract): Promise<Contract> {
    this.contracts.set(contract.getId().getValue(), contract);
    return contract;
  }

  // 测试辅助方法
  addTestContract(contract: Contract): void {
    this.contracts.set(contract.getId().getValue(), contract);
  }

  clear(): void {
    this.contracts.clear();
  }

  // ... 其他方法的Mock实现
}

/**
 * 单元测试示例
 */
describe('ContractApplicationService', () => {
  let contractService: ContractApplicationService;
  let mockContractRepository: MockContractRepository;

  beforeEach(() => {
    // 使用Mock仓储进行测试
    mockContractRepository = new MockContractRepository();
    contractService = new ContractApplicationService(
      mockContractRepository,
      new MockContractTemplateRepository(),
      new MockPersonalInfoRepository()
    );
  });

  it('should return contract templates for valid group id', async () => {
    // 准备测试数据
    const testContract = Contract.create({
      id: new ContractId('TEST_001'),
      type: new ContractType('USER_AGREEMENT'),
      groupId: 'XYDC',
      text: '用户协议',
      version: '1.0',
      forceReadConfig: new ForceReadConfig('Y', 30),
      isOldContract: false
    });

    mockContractRepository.addTestContract(testContract);

    // 执行测试
    const result = await contractService.getContractTemplates({
      contractGroupId: 'XYDC'
    });

    // 验证结果
    expect(result).toHaveLength(1);
    expect(result[0].contractId).toBe('TEST_001');
    expect(result[0].isForceReadRequired).toBe(true);
  });
});
