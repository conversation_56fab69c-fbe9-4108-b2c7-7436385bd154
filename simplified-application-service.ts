// ============================================================================
// 简化的应用服务设计 - 使用domain/repositories中的仓储
// ============================================================================

// ============================================================================
// application/services/contract-application.service.ts
// ============================================================================

import { ContractRepository, PersonalInfoRepository, RepositoryFactory } from '../../domain/repositories';
import { ContractTemplateService } from '../../domain/services/contract-template.service';
import { ContractTemplateDto } from '../dto/contract.dto';

/**
 * 合同应用服务 - 简化版
 * 
 * 直接使用领域层的仓储实现，无需通过接口抽象
 */
export class ContractApplicationService {
  private contractRepository: ContractRepository;
  private personalInfoRepository: PersonalInfoRepository;
  private contractTemplateService: ContractTemplateService;

  constructor() {
    // 直接使用仓储工厂获取仓储实例
    this.contractRepository = RepositoryFactory.getContractRepository();
    this.personalInfoRepository = RepositoryFactory.getPersonalInfoRepository();
    this.contractTemplateService = new ContractTemplateService();
  }

  /**
   * 获取合同模板
   */
  async getContractTemplates(contractGroupId: string): Promise<ContractTemplateDto[]> {
    try {
      // 1. 获取合同配置
      const contracts = await this.contractRepository.findByGroupId(contractGroupId);
      
      if (contracts.length === 0) {
        return [];
      }

      // 2. 获取个人信息
      const personalInfo = await this.personalInfoRepository.getCurrent();
      
      if (!personalInfo) {
        throw new Error('未找到个人信息');
      }

      // 3. 生成模板参数
      const templatePromises = contracts.map(async contract => {
        // 构建模板参数
        const templateParams = this.contractTemplateService.buildTemplateParams(
          contract, 
          personalInfo
        );
        
        // 转换为DTO
        return new ContractTemplateDto({
          contractId: contract.getId().getValue(),
          title: contract.getText(),
          content: await this.contractTemplateService.generateTemplateContent(templateParams),
          isForceReadRequired: contract.isForceReadRequired(),
          readDuration: contract.getReadDuration()
        });
      });

      return await Promise.all(templatePromises);
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw new Error('获取合同模板失败');
    }
  }

  /**
   * 验证合同同意状态
   */
  async validateContractAgreement(contractIds: string[]): Promise<boolean> {
    try {
      // 将字符串ID转换为领域值对象
      const contractIdObjects = contractIds.map(id => new ContractId(id));
      
      // 获取合同实体
      const contracts = await this.contractRepository.findByIds(contractIdObjects);
      
      // 检查是否所有必需的合同都已同意
      const requiredContracts = contracts.filter(c => c.isForceReadRequired());
      
      // 所有强制阅读的合同都必须在同意列表中
      return requiredContracts.every(contract => 
        contractIds.includes(contract.getId().getValue())
      );
    } catch (error) {
      console.error('验证合同同意状态失败:', error);
      return false;
    }
  }

  /**
   * 获取强制阅读合同
   */
  async getForceReadContracts(contractGroupId: string): Promise<ContractTemplateDto[]> {
    try {
      // 获取强制阅读合同
      const forceReadContracts = await this.contractRepository.findForceReadContracts(contractGroupId);
      
      if (forceReadContracts.length === 0) {
        return [];
      }

      // 转换为DTO
      const personalInfo = await this.personalInfoRepository.getCurrent();
      
      const templatePromises = forceReadContracts.map(async contract => {
        const templateParams = this.contractTemplateService.buildTemplateParams(
          contract, 
          personalInfo
        );
        
        return new ContractTemplateDto({
          contractId: contract.getId().getValue(),
          title: contract.getText(),
          content: await this.contractTemplateService.generateTemplateContent(templateParams),
          isForceReadRequired: true,
          readDuration: contract.getReadDuration()
        });
      });

      return await Promise.all(templatePromises);
    } catch (error) {
      console.error('获取强制阅读合同失败:', error);
      return [];
    }
  }
}

// ============================================================================
// application/dto/contract.dto.ts
// ============================================================================

/**
 * 合同模板数据传输对象
 */
export class ContractTemplateDto {
  contractId: string;
  title: string;
  content: string;
  isForceReadRequired: boolean;
  readDuration: number;

  constructor(data: {
    contractId: string;
    title: string;
    content: string;
    isForceReadRequired: boolean;
    readDuration: number;
  }) {
    this.contractId = data.contractId;
    this.title = data.title;
    this.content = data.content;
    this.isForceReadRequired = data.isForceReadRequired;
    this.readDuration = data.readDuration;
  }
}

// ============================================================================
// presentation/stores/contract-ui.store.ts
// ============================================================================

import { observable, action } from 'mobx';
import { ContractApplicationService } from '../../application/services/contract-application.service';
import { ContractTemplateDto } from '../../application/dto/contract.dto';

/**
 * 合同UI状态管理 - 简化版
 */
export class ContractUIStore {
  // UI状态
  @observable isShowContractsModal = false;
  @observable contractChecked = false;
  @observable hasCountDown = false;
  @observable onlyShowForceReadContracts = false;
  
  // 数据状态
  @observable contractTemplates: ContractTemplateDto[] = [];
  @observable forceReadTemplates: ContractTemplateDto[] = [];
  @observable isLoading = false;
  @observable error: string | null = null;

  // 应用服务
  private contractApplicationService: ContractApplicationService;

  constructor() {
    // 直接创建应用服务实例
    this.contractApplicationService = new ContractApplicationService();
  }

  /**
   * 加载合同模板
   */
  @action.bound
  async loadContractTemplates(contractGroupId: string): Promise<void> {
    try {
      this.isLoading = true;
      this.error = null;
      
      // 调用应用服务获取合同模板
      const templates = await this.contractApplicationService.getContractTemplates(contractGroupId);
      
      // 更新UI状态
      this.contractTemplates = templates;
      this.forceReadTemplates = templates.filter(t => t.isForceReadRequired);
      
    } catch (error) {
      this.error = error.message || '加载合同模板失败';
      console.error('加载合同模板失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 显示合同弹窗
   */
  @action.bound
  showContractsModal(onlyShowForceRead = false): void {
    this.onlyShowForceReadContracts = onlyShowForceRead;
    this.isShowContractsModal = true;
  }

  /**
   * 隐藏合同弹窗
   */
  @action.bound
  hideContractsModal(): void {
    this.isShowContractsModal = false;
  }

  /**
   * 切换合同勾选状态
   */
  @action.bound
  toggleContractChecked(): void {
    this.contractChecked = !this.contractChecked;
  }

  /**
   * 设置倒计时完成状态
   */
  @action.bound
  setCountDownComplete(): void {
    this.hasCountDown = true;
  }

  /**
   * 提交合同同意
   */
  @action.bound
  async submitAgreement(): Promise<boolean> {
    try {
      // 设置UI状态
      this.contractChecked = true;
      this.hasCountDown = true;
      this.hideContractsModal();
      
      // 验证合同同意状态
      const contractIds = this.contractTemplates.map(t => t.contractId);
      return await this.contractApplicationService.validateContractAgreement(contractIds);
    } catch (error) {
      console.error('提交合同同意失败:', error);
      return false;
    }
  }
}

// ============================================================================
// presentation/pages/identity/identity.page.tsx (部分代码)
// ============================================================================

/**
 * 身份信息页 - 使用简化的仓储和应用服务
 */
@observer
export class IdentityPage extends Component {
  private contractUIStore = new ContractUIStore();

  async componentDidMount() {
    // 加载合同模板
    await this.contractUIStore.loadContractTemplates('XYDC');
  }

  onContractClick = () => {
    // 显示合同弹窗
    this.contractUIStore.showContractsModal();
  }

  handleCheckboxClick = () => {
    // 如果已勾选，则取消勾选
    if (this.contractUIStore.contractChecked) {
      this.contractUIStore.toggleContractChecked();
      return;
    }

    // 如果有强制阅读合同且未读过，则显示强制阅读合同
    if (
      this.contractUIStore.forceReadTemplates.length > 0 && 
      !this.contractUIStore.hasCountDown
    ) {
      this.contractUIStore.showContractsModal(true);
      return;
    }

    // 否则直接勾选
    this.contractUIStore.toggleContractChecked();
  }

  submitAgreement = async () => {
    // 提交合同同意
    await this.contractUIStore.submitAgreement();
  }

  render() {
    const { 
      contractTemplates, 
      forceReadTemplates,
      isShowContractsModal, 
      contractChecked,
      hasCountDown,
      onlyShowForceReadContracts
    } = this.contractUIStore;
    
    return (
      <MUView>
        {/* 协议勾选组件 */}
        <ContractChecker
          contractText={contractTemplates.map(t => t.title).join('、')}
          contracts={[contractTemplates[0]]}
          checkedValue={contractChecked}
          beforeContractText="同意"
          outerControl
          onContractClick={this.onContractClick}
          handleCheckboxClick={this.handleCheckboxClick}
        />
        
        {/* 协议预览弹窗 */}
        {isShowContractsModal && (
          <AgreementDrawer
            agreementViewProps={{
              list: onlyShowForceReadContracts ? forceReadTemplates : contractTemplates,
              current: 0
            }}
            submit={this.submitAgreement}
            show={isShowContractsModal}
            close={() => this.contractUIStore.hideContractsModal()}
            totalCount={!hasCountDown ? forceReadTemplates[0]?.readDuration || 0 : 0}
          />
        )}
      </MUView>
    );
  }
}
